/*
 * $Id: NewBusiMgntToCrmServiceImpl.java,v 1.73 2015/07/22 08:55:13 fuqiang Exp $
 *
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;

import com.ailk.newchnl.aicbsDao.PointsSubinfoDao;
import com.ailk.newchnl.dao.*;
import com.ailk.newchnl.entity.*;
import com.ailk.newchnl.entity.crm.ChannelJson;
import com.ailk.newchnl.mybatis.pagination.PageData;
import com.ailk.newchnl.mybatis.pagination.PageParameter;
import com.ailk.newchnl.util.*;
import net.sf.json.JSONArray;
import net.sf.json.JSONException;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ailk.newchnl.support.Response;
import com.ailk.newchnl.aicbsDao.PointsInfoDao;
import com.ailk.newchnl.constant.ChannelConstants;
import com.ailk.newchnl.entity.channelPhoneLevel.ChannelAgentPhoneLevel;
import com.ailk.newchnl.entity.order.ChannelOrderDtl;
import com.ailk.newchnl.entity.resource.ChnlResModelDefinition;
import com.ailk.newchnl.entity.reward.RptDPayDirect;
import com.ailk.newchnl.entity.schedule.AgentNodeInfo;
import com.ailk.newchnl.entity.xt.ChannelNodeSynerGy;
import com.ailk.newchnl.entity.xt.ChannelNodeSynerGyDtl;
import com.ailk.newchnl.entity.xt.ChannelNodeSynerGyHis;
import com.ailk.newchnl.rewardDao.RptDPayDirectDao;
import com.ailk.newchnl.service.channelPhoneLevel.ChannelAgentPhoneLevelService;
import com.ailk.newchnl.service.crm.CRM2ChannelService;
import com.ailk.newchnl.utils.PinyinUtils;
import com.ailk.newchnl.util.CoordinateTransformUtil;
import com.ailk.newchnl.util.CoordinateTransformUtil.Coordinate;

/**
 * 外围接口
 *
 * <AUTHOR>
 * @version $Id: NewBusiMgntToCrmServiceImpl.java,v 1.73 2015/07/22 08:55:13 fuqiang Exp $
 * Created on 2014年7月31日 下午1:10:51
 */
@Service("newBusiMgntToCrmService")
public class NewBusiMgntToCrmServiceImpl implements NewBusiMgntToCrmService {

    private static final Logger logger = LoggerFactory.getLogger(NewBusiMgntToCrmServiceImpl.class);

    @Resource
    private ChnlAgentManagerInfoDao chnlAgentManagerInfoDao;
    @Resource
    private AgentChoosephoneAirDao agentChoosephoneAirDao;
    @Resource
    private ChannelAgentInfoDao channelAgentInfoDao;
    @Resource
    private ChannelEntityBasicInfoDao channelEntityBasicInfoDao;
    @Resource
    private ChannelOrgAgentDao channelOrgAgentDao;
    @Resource
    private ChannelEntityRelInfoDao channelEntityRelInfoDao;
    @Resource
    private ResInactivePhoneDao resInactivePhoneDao;
    @Resource
    private ResInactiveRecordDao resInactiveRecordDao;
    @Resource
    private AgentQueryRecordDao agentQueryRecordDao;
    @Resource
    private SmsOrderInterfaceDao smsOrderInterfaceDao;
    @Resource
    private ChannelResSimCardDao channelResSimCardDao;
    @Resource
    private ChannelEntityRelationInfoDao channelEntityRelationInfoDao;
    @Resource
    private ChnlActiveNumNotifyDao chnlActiveNumNotifyDao;
    @Resource
    private AgentInfoDtlDao agentInfoDtlDao;
    @Resource
    private ResAssignOrderDtlDao resAssignOrderDtlDao;
    @Resource
    private ChnlAgentBusiDataDao chnlAgentBusiDataDao;
    @Resource
    private ChnlNodeAuthorizationInfoDao chnlNodeAuthorizationInfoDao;
    @Resource
    private ChannelNodeDao channelNodeDao;
    @Resource
    private ChnlNodeYearCheckInfoDao chnlNodeYearCheckInfoDao;
    @Resource
    private ChannelManagerSvrMobileDao channelManagerSvrMobileDao;
    @Resource
    private ChannelSysBaseTypeDao channelSysBaseTypeDao;
    @Resource
    private AgentFeeDtlDao agentFeeDtlDao;
    @Resource
    private AgentDyjfAmountDao agentDyjfAmountDao;
    @Resource
    private RewardPaymentScheduleDao rewardPaymentScheduleDao;
    @Resource
    private AgentBusiAmountDao agentBusiAmountDao;
    @Resource
    private ChannelOrderDeliveryDao channelOrderDeliveryDao;
    @Resource
    private SimCardNoDao simCardNoDao;
    @Resource
    private ChannelOrderDao channelOrderDao;
    @Resource
    private ChannelOrderPayDao channelOrderPayDao;
    @Resource
    private ChannelEntityAccRelDao channelEntityAccRelDao;
    @Resource
    private ChannelBankAccountDao channelBankAccountDao;
    @Resource
    private ChannelBusiRecordDao channelBusiRecordDao;
    @Resource
    private AgentAccInfoDao agentAccInfoDao;
    @Resource
    private ChannelNodeExtinfoDao channelNodeExtinfoDao;
    @Resource
    private ChannelOrderDtlDao channelOrderDtlDao;
    @Resource
    private RptDPayDirectDao rptDPayDirectDao;
    @Resource
    private PointsInfoDao pointsInfoDao;
    @Resource
    private ChannelPointAccInfoDao channelPointAccInfoDao;
    @Resource
    private AgentBusiExtamountDao agentBusiExtamountDao;
    @Resource
    private AgentServiceFeeDao agentServiceFeeDao;
    @Resource
    private NodeFeeDtlDao nodeFeeDtlDao;
    @Resource
    private ChnlNodeFeeInfoDao chnlNodeFeeInfoDao;
    @Resource
    private ChannelAgentPhoneLevelService channelAgentPhoneLevelService;
    @Resource
    private ChannelIsSingleDao channelIsSingleDao;
    @Resource
    private ChnlResModelDefinitionDao chnlResModelDefinitionDao;
    @Resource
    private ChannelAgentTypeInfoDao channelAgentTypeInfoDao;
    @Resource
    private AgentNodeInfoDao agentNodeInfoDao;
    @Resource
    private ChannelEntityBasicInfoService channelEntityBasicInfoService;

    @Resource
    private ChannelNodeSynerGyDao channelNodeSynerGyDao;

    @Resource
    private ChannelNodeSynerGyHisDao channelNodeSynerGyHisDao;

    @Resource
    private CRM2ChannelService crm2ChannelService;

    @Resource
    private ChannelNodeChangeSynerGyDao channelNodeChangeSynerGyDao;

    @Resource
    private ChannelZydPhoneCheckDao channelZydPhoneCheckDao;


    @Resource
    private SellResourceSyncDao sellResourceSyncDao;

    @Resource
    private ChannelNodeService channelNodeService;

    @Resource
    private ChannelAgentService channelAgentService;

    @Resource
    private ChannelSoaService channelSoaService;

    @Resource
    private AgentSerialConfigDao agentSerialConfigDao;

    @Resource
    private ChannelShidianInfoDao channelShidianInfoDao;
    @Resource
    private AuthorizationCardInfoDao authorizationCardInfoDao;
    @Resource
    private AgentAccountInfoDao agentAccountInfoDao;
    @Resource
    private PointsSubinfoDao pointsSubinfoDao;
    @Resource
    private AgentPointNotUserDao agentPointNotUserDao;
    @Resource
    private AgentPointsExpireDao agentPointsExpireDao;

    @Resource
    private QueuingInfoDao queuingInfoDao;

    @Resource
    private ChannelAgentApplicationInfoDao channelAgentApplicationInfoDao;

    @Resource
    private ChannelAgentApplicationFileInfoDao channelAgentApplicationFileInfoDao;

    @Resource
    private BusinessHoursJointDao businessHoursJointDao;

    @Resource
    private GridTreeInfoDao gridTreeInfoDao;

    @Resource
    private ChannelUnifyCodeInfoDao channelUnifyCodeInfoDao;

    @Resource
    private ShareholderApproveInfoDao shareholderApproveInfoDao;


    /**
     * @param : busiCode		接口编码
     * @Desc : 获取开关
     * @return: flag            开关
     */
    public Map<String, Object> getFlag(String param) throws Exception {
        logger.debug("********    getFlag  begin  ********");
        String busiCode;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            busiCode = jsonParam.getString("busiCode");
        } catch (Exception e) {
            logger.error("接口编码有误" + e.getMessage());
            logger.debug("********    getFlag  end  ********");
            throw new Exception("接口编码有误");
        }
        Map<String, Object> map = new HashMap<String, Object>();
        ChannelToCrmConfigUtil channelToCrmConfigUtil = new ChannelToCrmConfigUtil();
        try {
            if (null == channelToCrmConfigUtil.getChannelToCrmConfig(busiCode, null).getFlag()) {
                channelToCrmConfigUtil.initChannelInterConfigList();
            }
            ChannelToCrmConfig channelToCrmConfig = channelToCrmConfigUtil.getChannelToCrmConfig(busiCode, null);
            map.put("flag", channelToCrmConfig.getFlag());
        } catch (Exception e) {
            map.put("flag", 0);
        }
        return map;
    }

    /**
     * @param : billId			手机号码
     * @Desc : 校验是否是客户经理号码
     * @return: managerName        客户经理姓名
     */
    public Map<String, Object> check_manager(String param) throws Exception {
        logger.debug("********    checkManger  begin  ********");
        String billId;
        ChnlAgentManagerInfo chnlAgentManagerInfo = new ChnlAgentManagerInfo();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            billId = jsonParam.getString("billId");

        } catch (Exception e) {
            logger.error("手机号码存在非数字字符" + e.getMessage());
            logger.debug("********    checkManger  end  ********");
            throw new Exception("手机号码存在非数字字符");
        }
        Map<String, Object> map = new HashMap<String, Object>();

        if (billId.equals("")) {
            throw new Exception("手机号码不能为空");
        }

        //检查号码是否满足数字类型
        for (int i = 0; i < billId.length(); i++) {
            if (billId.charAt(i) < '0' || billId.charAt(i) > '9') {
                throw new Exception("手机号码存在非数字字符");
            }
        }

        if (billId.toString().length() != 11) {
            throw new Exception("手机号码不满足11位");
        }
        chnlAgentManagerInfo.setbillId(billId);
        String e1 = "该号码非客户经理号码";
        String e2 = "该号码对应的客户经理信息重复，数据异常，校验失败";
        try {
            List<ChnlAgentManagerInfo> chnlAgentManagerInfoList = chnlAgentManagerInfoDao.query(chnlAgentManagerInfo);
            if (chnlAgentManagerInfoList.size() == 0) {
                throw new Exception(e1);
            }
            if (chnlAgentManagerInfoList.size() > 1) {
                throw new Exception(e2);
            }
            map.put("managerName", chnlAgentManagerInfoList.get(0).getManagerName());
        } catch (Exception e) {
            logger.error("校验是否是客户经理号码失败：" + e.getMessage());
            logger.debug("********    checkManger  end  ********");
            if (e.getMessage().equals(e1) || e.getMessage().equals(e2)) {
                map.put("code", e.getMessage());
                return map;
            }
            throw new Exception("校验客户经理手机号码失败");
        }
        logger.debug("********    checkManger  end  ********");
        return map;
    }

    /**
     * @param : regBillId			手机号码
     * @Desc : 提供给RBOSS前台根据代理商注册手机号鉴权结果
     * @return: agentId                代理商编号
     */
    public Map<String, Object> validate_agentBillId(String param) throws Exception {
        logger.debug("********    validateAgentBillId  begin  ********");
        Long regBillId;
        AgentChoosephoneAir agentChoosephoneAir = new AgentChoosephoneAir();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            regBillId = jsonParam.getLong("regBillId");
            agentChoosephoneAir.setRegBillId(regBillId.toString());
        } catch (Exception e) {
            logger.error("提供给RBOSS前台根据代理商注册手机号鉴权结果失败：" + e.getMessage());
            logger.debug("********    validateAgentBillId  end  ********");
            //			throw new Exception("未传入相应参数或传参格式不正确");
            throw new Exception("鉴权不通过，原因是该手机号并不是有效的空中选号代理商的注册手机号");
        }
        //		String e2 = "手机号码不能为空";
        if (null == regBillId || regBillId.equals("")) {
            throw new Exception("鉴权不通过，原因是该手机号并不是有效的空中选号代理商的注册手机号");
        }
        //		checkBillId(regBillId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("isRegMob", 0);
        String e1 = "鉴权不通过，原因是该手机号并不是有效的空中选号代理商的注册手机号";
        try {
            //判断手机号是否为空中选号代理商的注册手机号
            List<AgentChoosephoneAir> agentChoosephoneAirList = agentChoosephoneAirDao.query(agentChoosephoneAir);
            if (agentChoosephoneAirList.size() == 0) {
                throw new Exception(e1);
            }
            map.put("isRegMob", 1);
            map.put("agentId", agentChoosephoneAirList.get(0).getAgentId());
        } catch (Exception e) {
            logger.error("提供给RBOSS前台根据代理商注册手机号鉴权结果失败：" + e.getMessage());
            logger.debug("********    validateAgentBillId  end  ********");
            if (e1.equals(e.getMessage())) {
                throw e;
            }
            throw new Exception("代理商注册手机号鉴权失败");
        }
        logger.debug("********    validateAgentBillId  end  ********");
        return map;
    }

    /**
     * @param : agentId				代理商编号
     * @Desc : 根据代理商编号查询代理商全称和代理商类型（新系统中没有代理商类型）
     * @return: fullName            代理商全称
     * @return: agentType            代理商类型(空)
     */
    public Map<String, Object> get_agentInfo(String param) throws Exception {
        logger.debug("********    getAgentInfo  begin  ********");
        Long agentId;
        ChannelAgentInfo channelAgentInfo = new ChannelAgentInfo();
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            String temp = jsonParam.getString("agentId");
            if (temp.equals("")) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new Exception("没有查询到该代理商信息");
        }
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            agentId = jsonParam.getLong("agentId");
            channelAgentInfo.setAgentId(agentId);
        } catch (Exception e) {
            logger.error("根据代理商编号查询代理商全称和代理商类型失败：" + e.getMessage());
            logger.debug("********    getAgentInfo  end  ********");
            //			throw new Exception("没有查询到该代理商信息");
            map.put("code", "0010");
            return map;
        }

        String e1 = "没有查询到该代理商信息";

        try {
            //查询代理商名称
            List<ChannelAgentInfo> channelAgentInfoList = channelAgentInfoDao.query(channelAgentInfo);
            if (channelAgentInfoList.size() == 0) {
                throw new Exception(e1);
            }
            map.put("fullName", channelAgentInfoList.get(0).getFullName());
            ChannelAgentTypeInfo channelAgentTypeInfo = new ChannelAgentTypeInfo();
            channelAgentTypeInfo.setAgentId(agentId);
            channelAgentTypeInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelAgentTypeInfo> channelAgentTypeInfoList = channelAgentTypeInfoDao.query(channelAgentTypeInfo);
            if (0 != channelAgentTypeInfoList.size()) {
                map.put("agentType", channelAgentTypeInfoList.get(0).getAgentType());
            }
            map.put("agentType", "0");
        } catch (Exception e) {
            logger.error("根据代理商编号查询代理商全称和代理商类型失败：" + e.getMessage());
            logger.debug("********    getAgentInfo  end  ********");
            if (e1.equals(e.getMessage())) {
                throw e;
            }
            throw new Exception("查询代理商信息发生错误");
        }
        logger.debug("********    getAgentInfo  end  ********");
        return map;
    }

    /**
     * @param : orgId			组织编号
     * @Desc : 根据组织编号ORG_ID查询归属代理商编号
     * @return: agentId            代理商编号
     */
    public Map<String, Object> get_agentIdByOrgId(String param) throws Exception {
        logger.debug("********    getAgentIdByOrgId  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        Long orgId;
        ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();

        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            String temp = jsonParam.getString("orgId");
            if (temp.equals("")) {
                map.put("agentId", "0");
                return map;
            }
            orgId = Long.parseLong(temp);
            channelOrgAgent.setOrgId(orgId);
        } catch (Exception e) {
            logger.error("根据组织编号ORG_ID查询归属代理商编号失败：" + e.getMessage());
            logger.debug("********    getAgentIdByOrgId  end  ********");
            map.put("code", "0010");
            return map;
            //			throw new Exception("未传入相应参数或传参格式不正确");
        }

        List<Long> agentIdList = new ArrayList<Long>();
        String e1 = "没有组织编号所对应的代理商";
        //orgId所属的是代理商，则直接返回代理商编号
        try {
            //根据orgId查询所属代理商
            List<ChannelOrgAgent> channelOrgAgentList = channelOrgAgentDao.query(channelOrgAgent);
            Long agentId = null;
            for (int i = 0; i < channelOrgAgentList.size(); i++) {
                agentId = channelOrgAgentList.get(i).getAgentId();
                ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
                channelEntityBasicInfo.setChannelEntityId(agentId);
                channelEntityBasicInfo.setChannelEntityType(ChannelConstants.CHANNEL_ENTITY_TYPE_AGENT);
                channelEntityBasicInfo.setChannelEntityStatus(ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT);
                //判断代理商状态是否有效
                List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                if (channelEntityBasicInfoList.size() != 0) {
                    agentIdList.add(channelEntityBasicInfoList.get(0).getChannelEntityId());
                }
            }
            //orgId所属的是网点，则返回网点所属的代理商编号
            if (agentIdList.size() != 0) {
                map.put("agentId", agentIdList.get(0));
            } else {
                ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
                for (int i = 0; i < channelOrgAgentList.size(); i++) {
                    ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
                    channelEntityBasicInfo.setChannelEntityId(channelOrgAgentList.get(i).getAgentId());
                    channelEntityBasicInfo.setChannelEntityType(ChannelConstants.CHANNEL_ENTITY_TYPE_NODE);
                    Integer[] channelEntityStatus = {ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT, ChannelConstants.CHANNEL_ENTITY_STATUS_NORMAL_OPERATION,
                            ChannelConstants.CHANNEL_ENTITY_STATUS_CLOSE_UP_SHOP, ChannelConstants.CHANNEL_ENTITY_STATUS_PAUSE_BUSINESS};
                    channelEntityBasicInfo.setChannelEntityStatuss(channelEntityStatus);
                    List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                    if (channelEntityBasicInfoList.size() != 0) {
                        channelEntityRelInfo.setChannelEntityId(channelEntityBasicInfoList.get(0).getChannelEntityId());
                        List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.query(channelEntityRelInfo);
                        if (channelEntityRelInfoList.size() != 0) {
                            agentIdList.add(channelEntityRelInfoList.get(0).getParentEntity());
                        } else {
                            throw new Exception(e1);
                        }
                    } else {
                        throw new Exception(e1);
                    }
                }
                if (channelOrgAgentList.size() == 0) {
                    throw new Exception(e1);
                }
                map.put("agentId", agentIdList.get(0));
            }
        } catch (Exception e) {
            logger.error("根据组织编号ORG_ID查询归属代理商编号失败：" + e.getMessage());
            logger.debug("********    getAgentIdByOrgId  end  ********");
            if (e1.equals(e.getMessage())) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    getAgentIdByOrgId  end  ********");
        return map;
    }

    /**
     * @param : billId			手机号
     * @Desc : 提供给新CRM根据套封卡手机号找到所属代理商编号
     * @return: agentId            代理商编号
     */
    public Map<String, Object> get_AgentIdByBillId(String param) throws Exception {
        logger.debug("********    getAgentIdByBillId  begin  ********");
        String billId;
        ResInactivePhone resInactivePhone = new ResInactivePhone();
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            billId = jsonParam.getString("billId");
        } catch (Exception e) {
            logger.error("提供给新CRM根据套封卡手机号找到所属代理商编号失败：" + e.getMessage());
            logger.debug("********    getAgentIdByBillId  end  ********");
            //			throw new Exception("未传入相应参数或传参格式不正确");
            map.put("agentId", 0);
            return map;
        }
        //检验号码合法性
        //		checkBillId(billId);
        if (billId.equals("")) {
            throw new Exception("手机号不能为空！");
        }

        resInactivePhone.setResId(billId);
        String e2 = "没有查到对应的代理商";
        try {
            //根据套封卡手机号查询所属代理商编号
            List<ResInactivePhone> resInactivePhoneList = resInactivePhoneDao.query(resInactivePhone);
            if (resInactivePhoneList.size() > 0) {
                map.put("agentId", resInactivePhoneList.get(0).getOrgId());
            } else {
                //				throw new Exception(e2);
                map.put("agentId", 0);
                return map;
            }
        } catch (Exception e) {
            logger.error("提供给新CRM根据套封卡手机号找到所属代理商编号失败：" + e.getMessage());
            logger.debug("********    getAgentIdByBillId  end  ********");
            if (e.getMessage().equals(e2)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    getAgentIdByBillId  end  ********");
        return map;
    }

    /**
     * @param : billId			手机号
     * @param : opId			操作员编号(老系统有传，但没有到，暂时保留)
     * @param : orgId			操作员组织编号(老系统有传，但没有到，暂时保留)
     * @param : opEntityId		老系统有传，但没有到，暂时保留
     * @param : vestOrgId		老系统有传，但没有到，暂时保留
     * @Desc : 判断某手机号是否在有号资源中
     * @return: isExist            存在标识 1:存在，0：不存在
     */
    public Map<String, Object> get_isResInactivePhone(String param) throws Exception {
        logger.debug("********    getIsResInactivePhone  begin  ********");
        String billId;
        ResInactivePhone resInactivePhone = new ResInactivePhone();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("isExist", 0);
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            billId = jsonParam.getString("billId");
        } catch (Exception e) {
            logger.error("判断某手机号是否在有号资源中失败：" + e.getMessage());
            logger.debug("********    getIsResInactivePhone  end  ********");
            throw new Exception("未传入相应参数或传参格式不正确");
        }
        //检验号码合法性
        //		checkBillId(billId);
        resInactivePhone.setResId(billId);
        try {
            //判断手机号是否在活卡库存表中
            List<ResInactivePhone> resInactivePhoneList = resInactivePhoneDao.query(resInactivePhone);
            if (resInactivePhoneList.size() != 0) {
                map.put("isExist", 1);
            }
        } catch (Exception e) {
            logger.error("判断某手机号是否在有号资源中失败：" + e.getMessage());
            logger.debug("********    getIsResInactivePhone  end  ********");
            throw new Exception("查询失败");
        }
        logger.debug("********    getIsResInactivePhone  end  ********");
        return map;
    }

    /**
     * @param : billId			手机号
     * @param : opId			操作员编号
     * @param : orgId			操作员组织编号
     * @param : opEntityId		老系统有传，但没有到，暂时保留
     * @param : vestOrgId		老系统有传，但没有到，暂时保留
     * @Desc : 手工激活套封卡更新激活记录
     * @return: isActive        成功标识 1：成功，0：失败
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> update_phoneActiveStatus(String param) throws Exception {
        logger.debug("********    updatePhoneActiveStatus  begin  ********");
        String billId;
        Long opId;
        Long orgId;
        ResInactivePhone resInactivePhone = new ResInactivePhone();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("isSuccess", 0);
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            billId = jsonParam.getString("billId");
            opId = jsonParam.getLong("opId");
            orgId = jsonParam.getLong("orgId");
        } catch (Exception e) {
            logger.error("手工激活套封卡更新激活记录失败：" + e.getMessage());
            logger.debug("********    updatePhoneActiveStatus  end  ********");
            throw new Exception("未传入相应参数或传参格式不正确");
        }
        //检验号码合法性
        checkBillId(billId);
        resInactivePhone.setResId(billId);

        String e1 = "非渠道套封卡号码";
        String e2 = "该号码未分配给代理商";
        String e3 = "该号码已经激活，不能再执行激活操作！";
        try {
            //在活卡库存表中查询手机号对应的信息
            List<ResInactivePhone> resInactivePhoneList = resInactivePhoneDao.query(resInactivePhone);
            if (resInactivePhoneList.size() == 0) {
                throw new Exception(e1);
            }
            if (resInactivePhoneList.get(0).getManageStatus() < ChannelConstants.MANAGE_STATUS_TAKED) {
                throw new Exception(e2);
            }
            if (resInactivePhoneList.get(0).getIsActive() == ChannelConstants.PHONE_IS_ACTIVE) {
                throw new Exception(e3);
            }
            resInactivePhone = resInactivePhoneList.get(0);
            resInactivePhone.setIsActive(ChannelConstants.PHONE_IS_ACTIVE);
            //更新套封卡手机号激活状态
            int isActive = this.resInactivePhoneDao.update(resInactivePhone);
            if (1 != isActive) {
                throw new Exception("更新res_Inactive_Phone表失败");
            }
            ResInactiveRecord resInactiveRecord = new ResInactiveRecord();
            resInactiveRecord.setDoneCode(channelBusiRecordDao.getSequence());
            resInactiveRecord.setDoneDate(DateUtil.getCurrDate());
            resInactiveRecord.setResId(Long.parseLong(resInactivePhone.getResId()));
            resInactiveRecord.setAgentId(Long.parseLong(resInactivePhone.getOrgId().toString()));
            resInactiveRecord.setOpId(opId);
            resInactiveRecord.setOrgId(orgId);
            //插入套封卡激活业务记录表
            int isSuccess = this.resInactiveRecordDao.insert(resInactiveRecord);
            if (1 == isSuccess) {
                map.put("isSuccess", 1);
            }
        } catch (Exception e) {
            logger.error("手工激活套封卡更新激活记录失败：" + e.getMessage());
            logger.debug("********    updatePhoneActiveStatus  end  ********");
            if (e.getMessage().equals(e1) || e.getMessage().equals(e2) || e.getMessage().equals(e3)) {
                throw e;
            }
            throw new Exception("更新失败");
        }
        logger.debug("********    updatePhoneActiveStatus  end  ********");
        return map;
    }

    /**
     * @param : billId			手机号
     * @param : ext2			激活方式 1-短信随机码激活 5-10086ivr语音激活 空-话单激活
     * @param : opId			操作员编号
     * @param : orgId			操作员组织编号
     * @param : opEntityId		老系统有传，但没有到，暂时保留
     * @param : vestOrgId		老系统有传，但没有到，暂时保留
     * @Desc : 手工激活套封卡更新激活记录New
     * @return: isActive        成功标识 1：成功，0：失败
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> update_phoneActiveStatusNew(String param) throws Exception {
        logger.debug("********    updatePhoneActiveStatusNew  begin  ********");
        JSONObject jsonParam = JSONObject.fromObject(param);
        String billId;
        Long opId;
        Long orgId;
        int ext2;
        ResInactivePhone resInactivePhone = new ResInactivePhone();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("isSuccess", 0);
        try {
            billId = jsonParam.getString("billId");
            opId = jsonParam.getLong("opId");
            orgId = jsonParam.getLong("orgId");
            ext2 = jsonParam.getInt("ext2");
        } catch (Exception e) {
            logger.error("手工激活套封卡更新激活记录New失败：" + e.getMessage());
            logger.debug("********    updatePhoneActiveStatusNew  end  ********");
            throw new Exception("未传入相应参数或传参格式不正确");
        }
        //检验号码合法性
        checkBillId(billId);
        resInactivePhone.setResId(billId);

        String e1 = "非渠道套封卡号码";
        String e2 = "该号码未分配给代理商";
        String e3 = "该号码已经激活，不能再执行激活操作！";
        try {
            //在活卡库存表中查询手机号对应的信息
            List<ResInactivePhone> resInactivePhoneList = resInactivePhoneDao.query(resInactivePhone);
            if (resInactivePhoneList.size() == 0) {
                throw new Exception(e1);
            }
            if (resInactivePhoneList.get(0).getManageStatus() < ChannelConstants.MANAGE_STATUS_TAKED) {
                throw new Exception(e2);
            }
            if (resInactivePhoneList.get(0).getIsActive() == ChannelConstants.PHONE_IS_ACTIVE) {
                throw new Exception(e3);
            }
            resInactivePhone = resInactivePhoneList.get(0);
            resInactivePhone.setIsActive(ChannelConstants.PHONE_IS_ACTIVE);
            if (0 != ext2) {
                resInactivePhone.setExt2(ext2);
            }
            //更新套封卡手机号激活状态
            int isActive = this.resInactivePhoneDao.update(resInactivePhone);
            if (1 != isActive) {
                throw new Exception("更新res_Inactive_Phone表失败");
            }
            ResInactiveRecord resInactiveRecord = new ResInactiveRecord();
            resInactiveRecord.setDoneCode(channelBusiRecordDao.getSequence());
            resInactiveRecord.setDoneDate(DateUtil.getCurrDate());
            resInactiveRecord.setResId(Long.parseLong(resInactivePhone.getResId()));
            resInactiveRecord.setAgentId(Long.parseLong(resInactivePhone.getOrgId().toString()));
            resInactiveRecord.setOpId(opId);
            resInactiveRecord.setOrgId(orgId);
            //插入套封卡激活业务记录表
            int isSuccess = this.resInactiveRecordDao.insert(resInactiveRecord);
            if (1 == isSuccess) {
                map.put("isSuccess", 1);
            }
        } catch (Exception e) {
            logger.error("手工激活套封卡更新激活记录New失败：" + e.getMessage());
            logger.debug("********    updatePhoneActiveStatusNew  end  ********");
            if (e.getMessage().equals(e1) || e.getMessage().equals(e2) || e.getMessage().equals(e3)) {
                throw e;
            }
            throw new Exception("更新失败");
        }
        logger.debug("********    updatePhoneActiveStatusNew  end  ********");
        return map;
    }

    /**
     * @param : channelEntityId			渠道编号
     * @Desc : 关于“代理查询”记录登录信息
     * @return: isSuccess                成功标识 1：成功，0：失败
     */
    public Map<String, Object> get_policyInfo(String param) throws Exception {
        logger.debug("********    getPolicyInfo  bedin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("isSuccess", 0);
        Long channelEntityId;
        //输入校验
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            channelEntityId = jsonParam.getLong("channelEntityId");
        } catch (Exception e) {
            logger.error("关于“代理查询”记录登录信息失败：" + e.getMessage());
            logger.debug("********    getPolicyInfo  end  ********");
            //			throw new Exception("未输入相应参数或参数格式不正确");
            return map;
        }
        try {
            AgentQueryRecord agentQueryRecord = new AgentQueryRecord();
            agentQueryRecord.setChannelEntityId(channelEntityId);
            agentQueryRecord.setDoneDate(DateUtil.getCurrDate());
            agentQueryRecord.setBusiType(ChannelConstants.AGENT_QUERY_RECORD_POLICY_INFO);
            agentQueryRecord.setBusiName(ChannelConstants.AGENT_QUERY_RECORD_POLICY_INFO_STR);
            agentQueryRecord.setFunctionName("get_policyInfo");
            //插入记录表
            int isSuccess = agentQueryRecordDao.insert(agentQueryRecord);
            if (1 == isSuccess) {
                map.put("isSuccess", 1);
            }
        } catch (Exception e) {
            logger.error("关于“代理查询”记录登录信息失败：" + e.getMessage());
            logger.debug("********    getPolicyInfo  end  ********");
            throw new Exception("插入代理查询记录表失败");
        }
        logger.debug("********    getPolicyInfo  end  ********");
        return map;
    }

    /**
     * @param : agentId				单点商编号
     * @param : billId				订单联系人手机号码
     * @param : channelEntityId		单店商门店编号
     * @param : resType				资源类型   即有号卡，还是有价卡，以逗号分割的字符串，同资源型号一一对应
     * @param : resTypeId			资源子类别 ,即全球通，还是神州行,也是字符串组合，同资源型号一一对应
     * @param : resCode				订购品种，以逗号分割的形式,比如100213,100142
     * @param : resAmount			订购数量,与订购品种一一对应，以逗号分割
     * @param : unitPrice			资源单价,与订购品种一一对应，以逗号分割
     * @param : czCardFee			有价卡数量，老短营内部接口 get_card_info中输出
     * @param : yhCardCount			有号卡数量，老短营内部接口 get_card_info中输出
     * @Desc : 提供给新短营插入短信订单接口表的接口
     * @return: isSuccess            成功标识 1：成功，0：失败
     * 说明：此业务场景只适合单店商，下面只有一个门店.
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> commit_QDOnSMSOrder(String param) throws Exception {
        logger.debug("********    commitQDOnSMSOrder  begin  ********");
        Long agentId;
        Long billId;
        String strBillId;
        Long nodeId;
        String resType;
        String resTypeId;
        String resCode;
        String resAmount;
        String unitPrice;
        int czCardFee;
        String yhCardCount;
        SmsOrderInterface smsOrderInterface = new SmsOrderInterface();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("isSuccess", 0);
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            agentId = jsonParam.getLong("agentId");
            strBillId = jsonParam.getString("billId");
            //			billId = jsonParam.getLong("billId");
            nodeId = jsonParam.getLong("nodeId");
            resType = jsonParam.getString("resType");
            resTypeId = jsonParam.getString("resTypeId");
            resCode = jsonParam.getString("resCode");
            resAmount = jsonParam.getString("resAmount");
            unitPrice = jsonParam.getString("unitPrice");
            czCardFee = jsonParam.getInt("czCardFee");
            yhCardCount = jsonParam.getString("yhCardCount");
        } catch (Exception e) {
            logger.error("提供给新短营插入短信订单接口表的接口失败：" + e.getMessage());
            logger.debug("********    commitQDOnSMSOrder  end  ********");
            throw new Exception("未输入相应参数或参数格式不正确");
        }
        if ("".equals(resType)) {
            throw new Exception("卡类型不可为空");
        }
        if ("".equals(resTypeId)) {
            throw new Exception("卡类别不可为空");
        }
        if ("".equals(resCode)) {
            throw new Exception("卡型号不可为空");
        }
        if ("".equals(resAmount)) {
            throw new Exception("订购卡数量不可为空");
        }
        if ("".equals(unitPrice)) {
            throw new Exception("卡单价不可为空");
        }
        if ("".equals(strBillId)) {
            throw new Exception("手机号不可为空！");
        }
        if (nodeId == 0L) {
            throw new Exception("网点ID传入不正确！");
        }
        try {
            billId = Long.parseLong(strBillId);
        } catch (Exception e) {
            throw new Exception("");
        }
        try {
            smsOrderInterface.setAgentId(agentId);
            smsOrderInterface.setOrderDate(DateUtil.getCurrDate());
            smsOrderInterface.setBillId(billId);
            smsOrderInterface.setChannelEntityId(nodeId);
            smsOrderInterface.setOrderStatus(1);
            smsOrderInterface.setResType(resType);
            smsOrderInterface.setResTypeId(resTypeId);
            smsOrderInterface.setResCode(resCode);
            smsOrderInterface.setResAmount(resAmount);
            smsOrderInterface.setUnitPrice(unitPrice);
            smsOrderInterface.setExt3(czCardFee);
            smsOrderInterface.setExt4(yhCardCount);
            //插入短信订单接口表
            int isSuccess = smsOrderInterfaceDao.insert(smsOrderInterface);
            if (1 == isSuccess) {
                map.put("isSuccess", 1);
            }
        } catch (Exception e) {
            logger.error("提供给新短营插入短信订单接口表的接口失败：" + e.getMessage());
            logger.debug("********    commitQDOnSMSOrder  end  ********");
            throw new Exception("插入短信订单接口表失败");
        }
        logger.debug("********    commitQDOnSMSOrder  end  ********");
        return map;
    }

    /**
     * @param : resId				白卡卡号
     * @Desc : 提供给新CRM根据白卡卡号找到所属代理商编号
     * @return: agentId                代理商编号
     */
    public Map<String, Object> get_whitecardBelongAgent(String param) throws Exception {
        logger.debug("********    getWhitecardBelongAgent  begin  ********");
        String resId;
        ChannelResSimCard channelResSimCard = new ChannelResSimCard();
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            resId = jsonParam.getString("resId");
            channelResSimCard.setResId(resId);
            channelResSimCard.setResStatus(4);
        } catch (Exception e) {
            logger.error("提供给新CRM根据白卡卡号找到所属代理商编号失败：" + e.getMessage());
            logger.debug("********    getWhitecardBelongAgent  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        if (resId.equals("")) {
            throw new Exception("白卡卡号不可为空");
        }
        try {
            //根据白卡卡号找到所属代理商编号
            List<ChannelResSimCard> channelResSimCardList = channelResSimCardDao.query(channelResSimCard);
            Long agentId = channelResSimCardList.get(0).getBelongOrgId();
            if (agentId != null) {
                map.put("agentId", agentId);
            }
        } catch (Exception e) {
            logger.error("提供给新CRM根据白卡卡号找到所属代理商编号失败：" + e.getMessage());
            logger.debug("********    getWhitecardBelongAgent  end  ********");
            //			throw new Exception("没有找到对应的代理商，请检查输入的白卡卡号是否正确");
            map.put("agentId", 0);
        }
        logger.debug("********    getWhitecardBelongAgent  end  ********");
        return map;
    }

    /**
     * @param : channelEntityId			渠道编号
     * @Desc : 通过实体查询上一级父组织信息
     * @return: parentId                归属区域编号
     * @return: channelEntityId            渠道编号
     * @return: channelEntityType        渠道类型(1:代理商，2:网点)
     * @return: channelEntitySecondType    代理商类型或网点类型(当为代理商类型时，返回0)
     * @return: channelEntityName        渠道名称
     * @return: parentEntity            父渠道编号
     * @return: channelEntityStatus        渠道类型(表中全为3：准入)
     */
    public Map<String, Object> get_channelParentEntityById(String param) throws Exception {
        logger.debug("********    getChannelParentEntityById  begin  ********");
        Long channelEntityId;
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            if (jsonParam.getString("channelEntityId").equals("")) {
                map.put("code", "0000");
                return map;
            }
            channelEntityId = jsonParam.getLong("channelEntityId");
        } catch (Exception e) {
            logger.error("通过实体查询上一级父组织信息失败：" + e.getMessage());
            logger.debug("********    getChannelParentEntityById  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        try {
            //根据渠道编号，查询渠道信息
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(channelEntityId);
            Integer[] channelEntityStatuss = {ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT, ChannelConstants.CHANNEL_ENTITY_STATUS_NORMAL_OPERATION,
                    ChannelConstants.CHANNEL_ENTITY_STATUS_CLOSE_UP_SHOP, ChannelConstants.CHANNEL_ENTITY_STATUS_PAUSE_BUSINESS};
            channelEntityBasicInfo.setChannelEntityStatuss(channelEntityStatuss);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (0 != channelEntityBasicInfoList.size()) {
                map.put("m_iParentId", channelEntityBasicInfoList.get(0).getDistrictId());
                map.put("m_iChannelEntityId", channelEntityBasicInfoList.get(0).getChannelEntityId());
                map.put("m_strChannelEntityName", channelEntityBasicInfoList.get(0).getChannelEntityName());
                map.put("m_nChannelEntityType", channelEntityBasicInfoList.get(0).getChannelEntityType());
                map.put("m_nChannelEntityStatus", channelEntityBasicInfoList.get(0).getChannelEntityStatus());
            }
            //查询父渠道编号
            ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
            channelEntityRelInfo.setChannelEntityId(channelEntityId);
            List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.query(channelEntityRelInfo);
            if (0 != channelEntityRelInfoList.size()) {
                map.put("m_iParentEntity", channelEntityRelInfoList.get(0).getParentEntity());
            }
            if (1 == channelEntityBasicInfoList.get(0).getChannelEntityType()) {
                //为代理商，则类型返回为空
                ChannelAgentTypeInfo channelAgentTypeInfo = new ChannelAgentTypeInfo();
                channelAgentTypeInfo.setAgentId(channelEntityId);
                channelAgentTypeInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                List<ChannelAgentTypeInfo> channelAgentTypeInfoList = channelAgentTypeInfoDao.query(channelAgentTypeInfo);
                if (0 != channelAgentTypeInfoList.size()) {
                    map.put("m_nChannelEntitySecondType", channelAgentTypeInfoList.get(0).getAgentType());
                }
                map.put("m_nChannelEntitySecondType", "0");
            } else {
                //为网点，则类型返回为网点类型
                ChannelNode channelNode = new ChannelNode();
                channelNode.setNodeId(channelEntityId);
                channelNode.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                List<ChannelNode> channelNodeList = channelNodeDao.query(channelNode);
                map.put("m_nChannelEntitySecondType", channelNodeList.get(0).getNodeType());
            }

        } catch (Exception e) {
            logger.error("通过实体查询上一级父组织信息失败：" + e.getMessage());
            logger.debug("********    getChannelParentEntityById  end  ********");
            map.put("Flag", 0);
            return map;
            //			throw new Exception("没有查询到相应的渠道信息");
        }
        logger.debug("********    getChannelParentEntityById  end  ********");
        map.put("Flag", 1);
        return map;
    }

    /**
     * @param : regMobile				注册手机号
     * @param : whiteCardId				白卡卡号
     * @Desc : 提供给新CRM根据注册手机号和白卡卡号进行防串卡校验
     * @return: isSuccess                成功标识 1：成功，0：失败
     */
    public Map<String, Object> check_white_card_match(String param) throws Exception {
        logger.debug("********    checkWhiteCardMatch  begin  ********");
        String whiteCardId;
        String regMobile;
        ChannelResSimCard channelResSimCard = new ChannelResSimCard();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("nFlag", 1);
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            whiteCardId = jsonParam.getString("whiteCardId");
            regMobile = jsonParam.getString("regMobile");
        } catch (Exception e) {
            logger.error("供给新CRM根据注册手机号和白卡卡号进行防串卡校验失败：" + e.getMessage());
            logger.debug("********    checkWhiteCardMatch  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        //检验号码合法性
        //		checkBillId(regMobile);

        //		String e1 = "channel_res_sim_card表内无相关记录或重复";
        String e1 = "-1";
        //		String e2 = "空选白卡与代理商不匹配";
        String e2 = "-2";
        try {
            channelResSimCard.setResId(whiteCardId);
            //根据白卡卡号找到代销代理商
            List<ChannelResSimCard> channelResSimCardList = channelResSimCardDao.query(channelResSimCard);
            if (1 != channelResSimCardList.size()) {
                map.put("msg", "can not find with condition is:res_id = '" + whiteCardId + "'");
                map.put("hint", "channel_res_sim_card表内无相关记录或重复");
                return map;
            }
            channelResSimCard = channelResSimCardList.get(0);
            Long agentId = channelResSimCard.getBelongOrgId();
            AgentChoosephoneAir agentChoosephoneAir = new AgentChoosephoneAir();

            agentChoosephoneAir.setRegBillId(regMobile);
            agentChoosephoneAir.setAgentId(agentId);
            if (regMobile.equals("")) {
                map.put("msg", "can not find with condition is:reg_bill_id = '" + regMobile + "' and  (agent_id =" + agentId +
                        " or ext1 =" + agentId + ")");
                map.put("hint", "空选白卡与代理商不匹配");
                return map;
            }

            //判断注册手机号与代销代理商是否一致
            List<AgentChoosephoneAir> agentChoosephoneAirList = agentChoosephoneAirDao.query(agentChoosephoneAir);
            if (1 != agentChoosephoneAirList.size()) {
                agentChoosephoneAir.setAgentId(null);
                agentChoosephoneAir.setExt1(agentId.toString());
                List<AgentChoosephoneAir> agentChoosephoneAirList2 = agentChoosephoneAirDao.query(agentChoosephoneAir);
                if (1 != agentChoosephoneAirList2.size()) {
                    map.put("msg", "can not find with condition is:reg_bill_id = '" + regMobile + "' and  (agent_id =" + agentId +
                            " or ext1 =" + agentId + ")");
                    map.put("hint", "空选白卡与代理商不匹配");
                    return map;
                }
            }
            map.put("nFlag", 0);
        } catch (Exception e) {
            logger.error("供给新CRM根据注册手机号和白卡卡号进行防串卡校验失败：" + e.getMessage());
            logger.debug("********    checkWhiteCardMatch  end  ********");
            if (e.getMessage().equals(e1) || e.getMessage().equals(e2)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    checkWhiteCardMatch  end  ********");
        return map;
    }

    /**
     * @param : billId					代理商服务号码
     * @Desc : ivr调用渠道侧接口，以通知后台扫描进程查询代理商激活的卡的数据量
     * @return: isSuccess                成功标识 1：成功，0：失败
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> operate_chnlAgentActiveNumNotify(String param) throws Exception {
        logger.debug("********    operateChnlAgentActiveNumNotify  begin  ********");
        String billId;
        ChannelEntityRelationInfo channelEntityRelationInfo = new ChannelEntityRelationInfo();
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            billId = jsonParam.getString("billId");
            if (billId.equals("")) {
                map.put("code", "");
                return map;
            }
        } catch (Exception e) {
            logger.error("ivr调用渠道侧接口，以通知后台扫描进程查询代理商激活的卡的数据量失败：" + e.getMessage());
            logger.debug("********    operateChnlAgentActiveNumNotify  end  ********");
            throw new Exception("受理失败,根据该手机号码没有查找到代理商的信息");
            //			throw new Exception("未传入相应参数或传参格式不正确");
        }

        //检验号码合法性
        //		checkBillId(billId);
        String e1 = "受理失败,根据该手机号码没有查找到代理商的信息";
        String e2 = "根据手机号查询渠道编号:存在多条记录，主键冲突";
        try {
            channelEntityRelationInfo.setRelationMobile(billId);
            channelEntityRelationInfo.setRelationType(ChannelConstants.RELATION_TYPE_SERVICE_NUMBERS_CONTACTS);
            //判断手机号是否为代理商服务号码
            List<ChannelEntityRelationInfo> channelEntityRelationInfoList = channelEntityRelationInfoDao.query(channelEntityRelationInfo);
            if (1 > channelEntityRelationInfoList.size()) {
                throw new Exception(e1);
            }
            //查询服务号码对应的渠道 编号
            List<Long> EntityIds = new ArrayList<Long>();
            for (ChannelEntityRelationInfo c : channelEntityRelationInfoList) {
                EntityIds.add(c.getChannelEntityId());
            }

            Long[] channelEntityIds = new Long[EntityIds.size()];
            channelEntityIds = EntityIds.toArray(channelEntityIds);
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            channelEntityBasicInfo.setChannelEntityIds(channelEntityIds);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (0 == channelEntityBasicInfoList.size()) {
                throw new Exception(e1);
            }
            if (1 < channelEntityBasicInfoList.size()) {
                throw new Exception(e2);
            }


            ChnlActiveNumNotify chnlActiveNumNotify = new ChnlActiveNumNotify();
            chnlActiveNumNotify.setBillId(billId);
            chnlActiveNumNotify.setDoneDate(DateUtil.getCurrDate());
            chnlActiveNumNotify.setDealState(0);
            //插入chnl_active_num_notify表
            int isSuccess = chnlActiveNumNotifyDao.insert(chnlActiveNumNotify);
            if (1 == isSuccess) {
                map.put("nFlag", 0);
            }
        } catch (Exception e) {
            logger.error("ivr调用渠道侧接口，以通知后台扫描进程查询代理商激活的卡的数据量失败：" + e.getMessage());
            logger.debug("********    operateChnlAgentActiveNumNotify  end  ********");
            if (e.getMessage().equals(e1) || e.getMessage().equals(e2)) {
                throw e;
            }
            throw new Exception("操作失败");
        }
        logger.debug("********    operateChnlAgentActiveNumNotify  end  ********");
        return map;
    }

    /**
     * @param : parentEntity		父渠道编号
     * @param : agentId				代理商编号
     * @param : agentName			代理商名称
     * @param : agentType			代理商类型(新系统没有这个字段，不提供根据这个条件的查询)
     * @param : codeName			联系人类型
     * @Desc : 查询代理商信息
     * @return: parentEntity        父渠道编号
     * @return: channelEntityName    渠道名称（代理商简称）
     * @return: agentId                代理商编号
     * @return: agentType            代理商类型(新系统没有这个字段)
     * @return: fullName            代理商全称
     * @return: agentLevel            代理商等级
     * @return: codeName            联系人类型
     * @return: relationMobile        联系人电话
     */
    public List<JSONObject> get_channelAgentInfoDtl(String param) throws Exception {
        logger.debug("********    getChannelAgentInfoDtl  begin  ********");
        String nRecStatus;
        String parentEntity;
        String agentId;
        String agentName;
        String codeName;
        String default_ = "";
        List<JSONObject> list = new ArrayList<JSONObject>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            nRecStatus = jsonParam.getString("nRecStatus").equals("") ? default_ : jsonParam.getString("nRecStatus");
            parentEntity = jsonParam.getString("parentEntity").equals("") ? default_ : jsonParam.getString("parentEntity");
            agentId = jsonParam.getString("agentId").equals("") ? default_ : jsonParam.getString("agentId");
            agentName = jsonParam.getString("agentName").equals("") ? default_ : jsonParam.getString("agentName");
            codeName = jsonParam.getString("codeName").equals("") ? default_ : jsonParam.getString("codeName");
            if (!nRecStatus.equals("1") && !nRecStatus.equals("0")) {
                throw new Exception();
            }
        } catch (JSONException e) {
            logger.error("查询代理商信息失败：" + e.getMessage());
            logger.debug("********    getChannelAgentInfoDtl  end  ********");
            throw new Exception("传参格式不正确");
        }
        String e1 = "没有查询到相关的数据";
        try {
            AgentInfoDtl agentInfoDtl_ = new AgentInfoDtl();
            if (!"".equals(parentEntity)) {
                agentInfoDtl_.setParentEntity(Long.parseLong(parentEntity));
            }
            if (!"".equals(agentId)) {
                agentInfoDtl_.setAgentId(Long.parseLong(agentId));
            }
            if (!"".equals(nRecStatus)) {
                agentInfoDtl_.setRecStatus(Integer.parseInt(nRecStatus));
            }
            agentInfoDtl_.setFullName(agentName);
            agentInfoDtl_.setCodeName(codeName);
            agentInfoDtl_.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            //查询代理商信息
            List<AgentInfoDtl> agentInfoDtlList = agentInfoDtlDao.query(agentInfoDtl_);
            if (0 == agentInfoDtlList.size()) {
                throw new Exception(e1);
            }
            for (AgentInfoDtl agentInfoDtl : agentInfoDtlList) {
                ChannelAgentTypeInfo channelAgentTypeInfo = new ChannelAgentTypeInfo();
                channelAgentTypeInfo.setAgentId(agentInfoDtl.getAgentId());
                channelAgentTypeInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                List<ChannelAgentTypeInfo> channelAgentTypeInfoList = channelAgentTypeInfoDao.query(channelAgentTypeInfo);
                if (0 != channelAgentTypeInfoList.size()) {
                    agentInfoDtl.setAgentType(channelAgentTypeInfoList.get(0).getAgentType());
                }
                JSONObject reJson = JSONObject.fromObject(agentInfoDtl);
                list.add(reJson);
            }
        } catch (Exception e) {
            logger.error("查询代理商信息失败：" + e.getMessage());
            logger.debug("********    getChannelAgentInfoDtl  end  ********");
            if (e.getMessage().equals(e1)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    getChannelAgentInfoDtl  end  ********");
        return list;
    }

    /**
     * @param : billId					手机号码
     * @Desc : 根据号码_获取渠道套封卡领卡时间和返卡时间
     * @return: agentId                    领卡代理商编号
     * @return: assigenDate                领卡时间
     * @return: returnDate                返卡时间
     */
    public Map<String, Object> get_chnlPhoneTime(String param) throws Exception {
        logger.debug("********    getChnlPhoneTime  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String resId;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            resId = jsonParam.getString("billId");
        } catch (Exception e) {
            logger.error("根据号码_获取渠道套封卡领卡时间和返卡时间失败：" + e.getMessage());
            logger.debug("********    getChnlPhoneTime  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        //检验号码合法性
        checkBillId(resId);

        String e1 = "没有查到对应的号码";
        try {
            ResInactivePhone resInactivePhone = new ResInactivePhone();
            resInactivePhone.setResId(resId);
            Integer[] manageStatuss = {4, 5}; //4-预约；5－占用
            resInactivePhone.setManageStatuss(manageStatuss);
            //获取领卡代理商和返卡时间
            List<ResInactivePhone> resInactivePhoneList = resInactivePhoneDao.query(resInactivePhone);
            if (1 != resInactivePhoneList.size()) {
                throw new Exception(e1);
            }
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            map.put("agentId", resInactivePhoneList.get(0).getOrgId());
            if (null == resInactivePhoneList.get(0).getReturnDate()) {
                map.put("returnDate", "");
            } else {
                //				map.put("returnDate", sf.format(resInactivePhoneList.get(0).getReturnDate()));
                map.put("returnDate", "");
            }
            ResAssignOrderDtl resAssignOrderDtl = new ResAssignOrderDtl();
            resAssignOrderDtl.setBegId(resId);
            //获取领卡时间
            List<ResAssignOrderDtl> resAssignOrderDtlList = resAssignOrderDtlDao.queryOrderByAssigndate(resAssignOrderDtl);
            if (0 == resAssignOrderDtlList.size()) {
                map.put("assignDate", "");
            } else {
                map.put("assignDate", sf.format(resAssignOrderDtlList.get(0).getAssignDate()));
            }
        } catch (Exception e) {
            logger.error("根据号码_获取渠道套封卡领卡时间和返卡时间失败：" + e.getMessage());
            logger.debug("********    getChnlPhoneTime  end  ********");
            if (e.getMessage().equals(e1)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    getChnlPhoneTime  end  ********");
        return map;
    }

    /**
     * @param : billId					手机号码
     * @param : opId					操作员编号（老系统有传，但没有用到，暂时不传）
     * @param : orgId					操作员组织编号（老系统有传，但没有用到，暂时不传）
     * @param : opEntityId				（老系统有传，但没有用到，暂时不传）
     * @param : vestOrgId				（老系统有传，但没有用到，暂时不传）
     * @Desc : 获取号码的渠道信息
     * @return: billId                    手机号码
     * @return: agentName                代理商名称（全称）
     * @return: agentLevel                原代理商类型，新系统没有代理商类型，暂时返回为空
     * @return: nodeName                渠道名称（和代理商名称一样）
     * @return: nodeTypeName            渠道类型（和代理商类型一样，暂时返回为空）
     */
    public Map<String, Object> get_resChannelInfo(String param) throws Exception {
        logger.debug("********    getResChannelInfo  begin  ********");
        Map<String, Object> map = new HashMap();
        String billId;
        ResInactivePhone resInactivePhone = new ResInactivePhone();
        ChannelAgentInfo channelAgentInfo = new ChannelAgentInfo();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            billId = jsonParam.getString("billId");
        } catch (Exception e) {
            logger.error("获取号码的渠道信息失败：" + e.getMessage());
            logger.debug("********    getResChannelInfo  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        //检验号码合法性
        checkBillId(billId);
        String e1 = "没有号码对应的渠道信息";
        resInactivePhone.setResId(billId);
        try {
            //查询对应的代理商
            List<ResInactivePhone> resInactivePhoneoList = resInactivePhoneDao.query(resInactivePhone);
            if (0 == resInactivePhoneoList.size()) {
                throw new Exception(e1);
            }
            Long agentId = resInactivePhoneoList.get(0).getOrgId();
            channelAgentInfo.setAgentId(agentId);
            //查询代理商全称
            List<ChannelAgentInfo> channelAgentInfoList = channelAgentInfoDao.query(channelAgentInfo);
            if (0 == channelAgentInfoList.size()) {
                throw new Exception(e1);
            }
            map.put("billId", billId);
            map.put("agentName", channelAgentInfoList.get(0).getFullName());
            //注意代理商类型
            ChannelAgentTypeInfo channelAgentTypeInfo = new ChannelAgentTypeInfo();
            channelAgentTypeInfo.setAgentId(agentId);
            channelAgentTypeInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
//			List<ChannelAgentTypeInfo> channelAgentTypeInfoList = channelAgentTypeInfoDao.query(channelAgentTypeInfo);
            List<ChannelAgentTypeInfo> channelAgentTypeInfoList = channelAgentTypeInfoDao.getAgentTypeByEntityId(agentId);
            if (1 == channelAgentTypeInfoList.get(0).getAgentType()) {
                map.put("agentTypeName", "单点商");
            } else {
                map.put("agentTypeName", "非单点商");
            }
//			map.put("agentType", "1");
        } catch (Exception e) {
            logger.error("获取号码的渠道信息失败：" + e.getMessage());
            logger.debug("********    getResChannelInfo  end  ********");
            if (e.getMessage().equals(e1)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    getResChannelInfo  end  ********");
        return map;
    }

    /**
     * @param : nodeSerial				网点渠道序列号
     * @Desc : 提供给新CRM根据门店编码(网点渠道序列号)获取门店名称
     * @return: nodeName                网点名称
     * @return: agentId                    网点所属代理商编号
     * @return: ext1                    老系统返回空
     * @return: ext2                    老系统返回空
     */
    public Map<String, Object> get_ChnlNodeNameByNodeId(String param) throws Exception {
        logger.debug("********    getChnlNodeNameByNodeSerial  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String nodeSerial;
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            nodeSerial = jsonParam.getString("nodeSerial");
        } catch (Exception e) {
            logger.error("提供给新CRM根据门店编码(网点渠道序列号)获取门店名称失败：" + e.getMessage());
            logger.debug("********    getChnlNodeNameByNodeSerial  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        if (nodeSerial.equals("")) {
            map.put("code", "0010");
            map.put("msg", "org.omg.CORBA.BAD_PARAM:   vmcid: OMG  minor code: 0  completed: No");
            return map;
        }
        Integer[] channelEntityStatuss = {ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT, ChannelConstants.CHANNEL_ENTITY_STATUS_NORMAL_OPERATION,
                ChannelConstants.CHANNEL_ENTITY_STATUS_CLOSE_UP_SHOP, ChannelConstants.CHANNEL_ENTITY_STATUS_PAUSE_BUSINESS};
        channelEntityBasicInfo.setChannelEntitySerial(nodeSerial);
        channelEntityBasicInfo.setChannelEntityType(ChannelConstants.CHANNEL_ENTITY_TYPE_NODE);
        channelEntityBasicInfo.setChannelEntityStatuss(channelEntityStatuss);
        String e1 = "找不到匹配的门店名称。";
        try {
            //根据网点渠道序列号查询网点渠道编号
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                    channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (0 == channelEntityBasicInfoList.size()) {
                throw new Exception(e1);
            }
            map.put("nodeName", channelEntityBasicInfoList.get(0).getChannelEntityName());

            ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
            channelEntityRelInfo.setChannelEntityId(channelEntityBasicInfoList.get(0).getChannelEntityId());
            //根据网点渠道编号查询所属代理商编号
            List<ChannelEntityRelInfo> channelEntityRelInfoList =
                    channelEntityRelInfoDao.query(channelEntityRelInfo);
            map.put("agentId", channelEntityRelInfoList.get(0).getParentEntity());
        } catch (Exception e) {
            logger.error("提供给新CRM根据门店编码(网点渠道序列号)获取门店名称失败：" + e.getMessage());
            logger.debug("********    getChnlNodeNameByNodeSerial  end  ********");
            if (e.getMessage().equals(e1)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    getChnlNodeNameByNodeSerial  end  ********");
        return map;
    }

    /**
     * @param : billId					订单联系人电话
     * @Desc : 提供给新短营根据空中选号注册手机号获得对应的代理商以及门店(此业务场景只适合单店商，下面只有一个门店)
     * 新系统没有代理商类型，无法区分单店商，返回的信息中一个代理商可能存在多个网点，暂时只返回第一个网点
     * @return: agentId                    单店商编号（不一定单店商）
     * @return: nodeId                    单店商门店编号（不一定单店商门店）
     */
    public Map<String, Object> get_ChnlNodeIdByBillIdOnSMSOrder(String param) throws Exception {
        logger.debug("********    getChnlNodeIdByBillIdOnSMSOrder  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String billId;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            billId = jsonParam.getString("billId");
        } catch (Exception e) {
            logger.error("提供给新短营根据空中选号注册手机号获得对应的代理商以及门店失败：" + e.getMessage());
            logger.debug("********    getChnlNodeIdByBillIdOnSMSOrder  end  ********");
            throw new Exception("手机号不是代理商的订单系统联系人号码!");
        }
        ChannelEntityRelationInfo channelEntityRelationInfo = new ChannelEntityRelationInfo();
        channelEntityRelationInfo.setRelationMobile(billId);
        //订单联系人
        channelEntityRelationInfo.setRelationType(ChannelConstants.RELATION_TYPE_ORDER_CONTACTS);
        String e1 = "手机号不是代理商的订单系统联系人号码!";
        if (billId.equals("")) {
            throw new Exception("手机号不可为空!");
        }
        try {
            //根据订单联系人手机号查询对应的代理商
            List<ChannelEntityRelationInfo> channelEntityRelationInfoList =
                    channelEntityRelationInfoDao.query(channelEntityRelationInfo);
            if (0 == channelEntityRelationInfoList.size()) {
                throw new Exception(e1);
            }
            Long agentId = channelEntityRelationInfoList.get(0).getChannelEntityId();
            //判断代理商状态是否有效
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(agentId);
            channelEntityBasicInfo.setChannelEntityStatus(ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                    channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            ChannelAgentInfo channelAgentInfo = new ChannelAgentInfo();
            channelAgentInfo.setAgentId(agentId);
            List<ChannelAgentInfo> channelAgentInfoList = channelAgentInfoDao.query(channelAgentInfo);
            if (0 == channelEntityBasicInfoList.size() || 0 == channelAgentInfoList.size()) {
                throw new Exception(e1);
            }
            //根据代理商查询对应的网点（门店）
            ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
            channelEntityRelInfo.setParentEntity(agentId);
            List<ChannelEntityRelInfo> channelEntityRelInfoList =
                    channelEntityRelInfoDao.query(channelEntityRelInfo);
            Long nodeId;
            if (0 == channelEntityRelInfoList.size()) {
                nodeId = 0L;
            } else {
                /***
                 * 加一步逻辑 将代理商对应的网点在channel_entity_basi_info表查询，查询不是销户网点在进行处理
                 */
                Long[] channelEntityIds = new Long[channelEntityRelInfoList.size()];
                for (int i = 0; i < channelEntityRelInfoList.size(); i++) {
                    channelEntityIds[i] = channelEntityRelInfoList.get(i).getChannelEntityId();
                }
                channelEntityBasicInfo = new ChannelEntityBasicInfo();
                Integer[] statuss = {ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT, ChannelConstants.CHANNEL_ENTITY_STATUS_NORMAL_OPERATION,
                        ChannelConstants.CHANNEL_ENTITY_STATUS_CLOSE_UP_SHOP, ChannelConstants.CHANNEL_ENTITY_STATUS_PAUSE_BUSINESS};

                channelEntityBasicInfo.setChannelEntityStatuss(statuss);
                channelEntityBasicInfo.setChannelEntityType(ChannelConstants.CHANNEL_ENTITY_TYPE_NODE);
                channelEntityBasicInfo.setChannelEntityIds(channelEntityIds);
                //判断网点状态是否有效
                List<ChannelEntityBasicInfo> channelEntityBasicInfoList1 = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                if (channelEntityBasicInfoList1.size() <= 0) {
                    nodeId = 0L;
                } else {
                    nodeId = channelEntityBasicInfoList1.get(0).getChannelEntityId();
                }
//				channelEntityBasicInfo = new ChannelEntityBasicInfo();
//				channelEntityBasicInfo.setChannelEntityStatuss(statuss);
//				channelEntityBasicInfo.setChannelEntityType(ChannelConstants.CHANNEL_ENTITY_TYPE_NODE);
//				channelEntityBasicInfo.setChannelEntityId(nodeId);
//				//判断网点状态是否有效
//				List<ChannelEntityBasicInfo> channelEntityBasicInfoList_ =
//						channelEntityBasicInfoDao.query(channelEntityBasicInfo);
//				if(0 == channelEntityBasicInfoList_.size()){
//					nodeId = 0L;
//				}
                /*Long nodeId;
                int j=1;
				for(int i=0; i<channelEntityRelInfoList.size(); i++){
					nodeId = channelEntityRelInfoList.get(i).getChannelEntityId();
					channelEntityBasicInfo.setChannelEntityId(nodeId);
					List<ChannelEntityBasicInfo> channelEntityBasicInfoList_ =
							channelEntityBasicInfoDao.query(channelEntityBasicInfo);
					if(0 != channelEntityBasicInfoList_.size()){
						map.put("nodeId"+(j++), nodeId.toString());
					}
				}*/
            }
            map.put("agentId", agentId);
            map.put("nodeId", nodeId);
        } catch (Exception e) {
            logger.error("提供给新短营根据空中选号注册手机号获得对应的代理商以及门店失败：" + e.getMessage());
            logger.debug("********    getChnlNodeIdByBillIdOnSMSOrder  end  ********");
            if (e.getMessage().equals(e1)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    getChnlNodeIdByBillIdOnSMSOrder  end  ********");
        return map;
    }

    /**
     * @param : billMonth					账单月
     * @param : billId						代理商服务号码（单点商/非空选单点商）
     * @Desc : 代理商的业务量和酬金信息查询接口（暂时只支持单店查询）
     * @return: agentId                        代理商编号
     * @return: billMonth                    账单月
     * @return: cardNumNew                    新卡小计（发展统计分析）
     * @return: cardNumOld                    老卡小计（发展统计分析）
     * @return: cardNumTotal                号卡合计
     * @return: terminalNum                    终端销量（稽核后）
     * @return: perTakeHome                    个人实得(酬金 单位 分)
     * @return: insertDate                    插入时间
     */
    public Map<String, Object> get_chnlAgentBusiData(String param) throws Exception {
        logger.debug("********    getChnlAgentBusiData  begin  ********");
        JSONObject reJson;
        Integer billMonth;
        String billId;
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            billMonth = jsonParam.getInt("billMonth");
            billId = jsonParam.getString("billId");
        } catch (Exception e) {
            logger.error("代理商的业务量和酬金信息查询接口失败：" + e.getMessage());
            logger.debug("********    getChnlAgentBusiData  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        int year = billMonth / 100;
        int month = billMonth % 100;
        if (year >= 2099 || billMonth < 2000 || month < 1 || month > 12) {
            throw new Exception("输入月份超出范围");
        }
        //检验号码合法性
        checkBillId(billId);
        ChannelEntityRelationInfo channelEntityRelationInfo = new ChannelEntityRelationInfo();
        channelEntityRelationInfo.setRelationMobile(billId);
        //服务联系人
        channelEntityRelationInfo.setRelationType(ChannelConstants.RELATION_TYPE_SERVICE_NUMBERS_CONTACTS);
        String e1 = "非代理商服务号码";
        String e2 = "没有对应的业务量和酬金信息";
        try {
            //根据服务号码查询代理商信息
            List<ChannelEntityRelationInfo> channelEntityRelationInfoList =
                    channelEntityRelationInfoDao.query(channelEntityRelationInfo);
            if (0 == channelEntityRelationInfoList.size()) {
                throw new Exception(e1);
            }
            //由于代理商服务在数据库中有重复，同一句SQL，老库先查出网点，新库先查出代理商，暂时先跟老库一致，
            Long agentId = channelEntityRelationInfoList.get(channelEntityRelationInfoList.size() - 1).getChannelEntityId();
            ChannelAgentInfo channelAgentInfo = new ChannelAgentInfo();
            channelAgentInfo.setAgentId(agentId);
            List<ChannelAgentInfo> channelAgentInfoList = channelAgentInfoDao.query(channelAgentInfo);
            if (0 == channelAgentInfoList.size()) {
                throw new Exception(e1);
            }
            map.put("m_strAgentName", channelAgentInfoList.get(0).getFullName());
            //查询代理商的业务量和酬金信息 --出账结果
            ChnlAgentBusiData chnlAgentBusiData = new ChnlAgentBusiData();
            chnlAgentBusiData.setAgentId(agentId);
            chnlAgentBusiData.setBillMonth(billMonth);
            List<ChnlAgentBusiData> chnlAgentBusiDataList = chnlAgentBusiDataDao.query(chnlAgentBusiData);
            if (0 == chnlAgentBusiDataList.size()) {
                throw new Exception(e2);
            }
            ChnlAgentBusiData agentBusiData = chnlAgentBusiDataList.get(0);
            map.put("m_iCardNumTotal", agentBusiData.getCardNumTotal() == null ? 0 : agentBusiData.getCardNumTotal());
            map.put("m_iPerTakeHome", agentBusiData.getPerTakeHome() == null ? 0 : agentBusiData.getPerTakeHome());
            map.put("m_iBillMonth", agentBusiData.getBillMonth());
            map.put("m_iTerMinalNum", agentBusiData.getTerminalNum() == null ? 0 : agentBusiData.getTerminalNum());
            map.put("m_iAgentId", agentBusiData.getAgentId());
            map.put("m_iCardNumOld", agentBusiData.getCardNumOld() == null ? 0 : agentBusiData.getCardNumOld());
            map.put("m_iCardNumNew", agentBusiData.getCardNumNew() == null ? 0 : agentBusiData.getCardNumNew());
            map.put("m_iExt1", agentBusiData.getExt1() == null ? 0 : agentBusiData.getExt1());
            map.put("m_iExt2", agentBusiData.getExt2() == null ? 0 : agentBusiData.getExt2());
            map.put("m_strExt3", agentBusiData.getExt3() == null ? "" : agentBusiData.getExt3());
            map.put("m_strExt4", agentBusiData.getExt4() == null ? "" : agentBusiData.getExt4());
            map.put("m_strExt5", agentBusiData.getExt5() == null ? "" : agentBusiData.getExt5());
        } catch (Exception e) {
            logger.error("代理商的业务量和酬金信息查询接口失败：" + e.getMessage());
            logger.debug("********    getChnlAgentBusiData  end  ********");
            if (e.getMessage().equals(e1) || e.getMessage().equals(e2)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    getChnlAgentBusiData  end  ********");
        return map;
    }

    /**
     * @param : nodeAuthId				网点授权编码
     * @Desc : 检查网点授权编码信息
     * @return: nodeId                    网点编号
     * @return: nodeName                网点名称
     * @return: nodeAddr                网点地址
     * @return: isYearCheck                年检信息
     * @return: cardLimit                网点销售号码限额
     * @return: agentId                    网点对应代理商ID
     */
    public Map<String, Object> sms_nodeAuthYearCheck(String param) throws Exception {
        logger.debug("********    smsNodeAuthYearCheck  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String nodeAuthId;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            nodeAuthId = jsonParam.getString("nodeAuthId");
        } catch (Exception e) {
            logger.error("检查网点授权编码信息失败：" + e.getMessage());
            logger.debug("********    smsNodeAuthYearCheck  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        String e1 = "对不起，您输入的网点授权编号不存在，请重新发送;";
        String e2 = "无此网点授权编码对应的网点信息;";
        String e3 = "无此网点详细信息,";
        String e4 = "无此网点授权编码对应的父渠道编号;";
        if (nodeAuthId.equals("")) {
            throw new Exception("对不起，您输入的网点授权编号不存在，请重新发送;");
        }
        try {
            ChnlNodeAuthorizationInfo chnlNodeAuthorizationInfo = new ChnlNodeAuthorizationInfo();
            chnlNodeAuthorizationInfo.setNodeAuthoriztionId(nodeAuthId);
            //根据网点授权编码查询网点编号
            List<ChnlNodeAuthorizationInfo> chnlNodeAuthorizationInfoList =
                    chnlNodeAuthorizationInfoDao.query(chnlNodeAuthorizationInfo);
            if (0 == chnlNodeAuthorizationInfoList.size()) {
                throw new Exception(e1);
            }
            Long chnlEntId = chnlNodeAuthorizationInfoList.get(0).getNodeId();
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(chnlEntId);
            //根据网点编号查询网点信息
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                    channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (0 == channelEntityBasicInfoList.size()) {
                throw new Exception(e2);
            }
            ChannelNode channelNode = new ChannelNode();
            channelNode.setNodeId(chnlEntId);
            channelNode.setRecStatus(1);
            List<ChannelNode> channelNodeList = channelNodeDao.query(channelNode);
            if (0 == channelNodeList.size()) {
                throw new Exception(e3);
            }
            ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
            channelEntityRelInfo.setChannelEntityId(chnlEntId);
            List<ChannelEntityRelInfo> channelEntityRelInfoList =
                    channelEntityRelInfoDao.query(channelEntityRelInfo);
            if (0 == channelEntityRelInfoList.size()) {
                throw new Exception(e4);
            }
            ChnlNodeYearCheckInfo chnlNodeYearCheckInfo = new ChnlNodeYearCheckInfo();
            chnlNodeYearCheckInfo.setNodeId(chnlEntId);
            List<ChnlNodeYearCheckInfo> chnlNodeYearCheckInfoList = chnlNodeYearCheckInfoDao.query(chnlNodeYearCheckInfo);
            String strIsYearCheck = "年检不合格";
            if (0 != chnlNodeYearCheckInfoList.size()) {
                for (int i = 0; i < chnlNodeYearCheckInfoList.size(); i++) {
                    if (1 == chnlNodeYearCheckInfoList.get(i).getCheckStatus()) {
                        strIsYearCheck = "年检合格";
                    }
                }
            }
            map.put("nodeId", chnlEntId);
            map.put("nodeName", channelEntityBasicInfoList.get(0).getChannelEntityName());
            map.put("nodeAddr", channelNodeList.get(0).getNodeAddr());
            map.put("cardLimit", channelNodeList.get(0).getYhcardLimit() == null ? 0 : channelNodeList.get(0).getYhcardLimit());
            map.put("isYearCheck", strIsYearCheck);
            map.put("agentId", channelEntityRelInfoList.get(0).getParentEntity());
        } catch (Exception e) {
            logger.error("检查网点授权编码信息失败：" + e.getMessage());
            logger.debug("********    smsNodeAuthYearCheck  end  ********");
            if (e.getMessage().equals(e1) || e.getMessage().equals(e2)
                    || e.getMessage().equals(e3) || e.getMessage().equals(e4)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    smsNodeAuthYearCheck  end  ********");
        return map;
    }

    /**
     * @param : SvrNum 						服务号码
     * @Desc : 根据服务号码 返回代理商或网点信息
     * @return: nFlag                        返回 1-"代理商"服务号码登入 2-"网点"服务号码登入  3-"管理人员登录号码"
     * nFlag = 1 情况下出参
     * @return: managerName                    客户经理姓名
     * @return: managerPhone                客户经理手机号码
     * @return: managerOtherConnect            客户经理其他联系方式
     * @return: channelEntityId                代理商编号
     * @return: agentFullName                代理商全称
     * @return: ahannelEntityName            渠道实体名称（代理商简称）
     * @return: agentType                    代理商类型(为空)
     * @return: agentLevel                    代理商等级
     * nFlag = 2 情况下出参
     * @return: cusManagerName                网点督导姓名
     * @return: cusManagerPhone                网点督导手机号码
     * @return: cusManagerOtherConnect        网点督导其他联系方式
     * @return: channelEntityId                网点编号
     * @return: nodeType                    网点类型
     * @return: nodeName                    网点名称
     * @return: nodeAddr                    网点地址
     * @return: areaCodeID                    网点所属行政区代码
     * @return: areaName                    网点所属行政区名
     * @return: nodeType                    网点类型
     * nFlag = 3 情况下出参
     */
    public Map<String, Object> get_channelInfoBySvrNum(String param) throws Exception {
        logger.debug("********    getChannelInfoBySvrNum  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String svrNum;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            svrNum = jsonParam.getString("svrNum");
        } catch (Exception e) {
            logger.error("根据服务号码 返回代理商或网点信息失败：" + e.getMessage());
            logger.debug("********    getChannelInfoBySvrNum  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        String e1 = "查询是否是管理人员渠道专席号码";
        String e2 = "该手机号没有对应的代理商信息";
        String e3 = "网点资料不存在";
        checkBillId(svrNum);
        map.put("managerOtherConnect", "");
        map.put("nodeName", "");
        map.put("cusManagerPhone", "");
        map.put("channelEntityId", "");
        map.put("managerName", "");
        map.put("nodeAddr", "");
        map.put("agentType", 0);
        map.put("nodeType", 0);
        map.put("nFlag", "");
        map.put("cusManagerOtherConnect", "");
        map.put("areaCodeID", 0);
        map.put("channelEntityType", 0);//原来1为非单点商，2为非单点商下属网点，3为单点商，4为单点商下属网点
        map.put("managerPhone", "");
        map.put("address", "");
        map.put("cusManagerName", "");
        map.put("channelEntityName", "");
        map.put("agentName", "");
        map.put("fullName", "");
        map.put("areaName", "");
        map.put("strAgentLevel", "");
        map.put("nAgentLevel", 0);
        try {
            ChannelEntityRelationInfo channelEntityRelationInfo = new ChannelEntityRelationInfo();
            checkBillId(svrNum);
            channelEntityRelationInfo.setRelationMobile(svrNum);
            channelEntityRelationInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            channelEntityRelationInfo.setRelationType(ChannelConstants.RELATION_TYPE_SERVICE_NUMBERS_CONTACTS);
            //判断该号码是否为服务号码
            List<ChannelEntityRelationInfo> channelEntityRelationInfoList =
                    channelEntityRelationInfoDao.query(channelEntityRelationInfo);
            if (0 == channelEntityRelationInfoList.size()) {
                //查询是否是管理人员渠道专席号码
                //				ChannelManagerSvrMobile ChannelManagerSvrMobile = new ChannelManagerSvrMobile();
                throw new Exception(e1);
            }
            //由于代理商服务在数据库中有重复，同一句SQL，老库先查出网点，新库先查出代理商，暂时先跟老库一致，
            /*
             * 由于存在一个人可能开多家店（即联系人手机相同），假设情况下某一家门店退出。
             * 假如随机取了一条数据，取到了退出门店的渠道编号，则下边查询不到数据
             * 即需要将相同手机号的所有对应数据到channel_entity_basic_info中取查询到有效的数据
             * */
            Long[] channelEntityIds = new Long[channelEntityRelationInfoList.size()];
            for (int i = 0; i < channelEntityRelationInfoList.size(); i++) {
                channelEntityIds[i] = channelEntityRelationInfoList.get(i).getChannelEntityId();
            }
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityIds(channelEntityIds);
            channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            Integer[] statuss = {3, 11, 12, 13};
            channelEntityBasicInfo.setChannelEntityStatuss(statuss);
            //根据渠道编号判断是代理商还是网点
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                    channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (0 == channelEntityBasicInfoList.size()) {
                //查询是否是管理人员渠道专席号码
                throw new Exception(e1);
            }
            Long chnlEntId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            Integer nFlag = channelEntityBasicInfoList.get(0).getChannelEntityType();
            //代理商服务号码
            if (1 == nFlag) {
                ChannelAgentInfo channelAgentInfo = new ChannelAgentInfo();
                channelAgentInfo.setAgentId(chnlEntId);
                channelAgentInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                //查询代理商信息
                List<ChannelAgentInfo> channelAgentInfoList = channelAgentInfoDao.query(channelAgentInfo);
                if (0 == channelAgentInfoList.size()) {
                    throw new Exception(e2);
                }
                ChannelEntityRelationInfo channelEntityRelationInfo_ = new ChannelEntityRelationInfo();
                channelEntityRelationInfo_.setChannelEntityId(chnlEntId);
                channelEntityRelationInfo_.setRelationType(5);
                channelEntityRelationInfo_.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                //查询代理商客户经理信息
                channelEntityRelationInfoList = channelEntityRelationInfoDao.query(channelEntityRelationInfo_);
                if (0 != channelEntityRelationInfoList.size()) {
                    map.put("managerName", channelEntityRelationInfoList.get(0).getRelationName());
                    map.put("managerPhone", channelEntityRelationInfoList.get(0).getRelationMobile());
                    map.put("managerOtherConnect", channelEntityRelationInfoList.get(0).getOtherContact() == null ? "" : channelEntityRelationInfoList.get(0).getOtherContact());
                }
                map.put("nFlag", "1");
                map.put("channelEntityId", chnlEntId);
                ChannelAgentTypeInfo channelAgentTypeInfo = new ChannelAgentTypeInfo();
                channelAgentTypeInfo.setAgentId(chnlEntId);
                channelAgentTypeInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                List<ChannelAgentTypeInfo> channelAgentTypeInfoList = channelAgentTypeInfoDao.query(channelAgentTypeInfo);
                if (0 != channelAgentTypeInfoList.size()) {
                    map.put("agentType", channelAgentTypeInfoList.get(0).getAgentType());
                }
                map.put("agentType", "0");
                map.put("fullName", channelAgentInfoList.get(0).getFullName());
                map.put("channelEntityName", channelEntityBasicInfoList.get(0).getChannelEntityName());
                map.put("address", channelAgentInfoList.get(0).getAddress());
                ChannelSysBaseType channelSysBaseType = new ChannelSysBaseType();
                channelSysBaseType.setCodeType(ChannelConstants.AGENT_LEVEL_BASE_TYPE);
                if (channelAgentInfoList.get(0).getAgentLevel() != null) {
                    channelSysBaseType.setCodeId(channelAgentInfoList.get(0).getAgentLevel());
                    List<ChannelSysBaseType> channelSysBaseTypeList = channelSysBaseTypeDao.query(channelSysBaseType);
                    if (0 != channelSysBaseTypeList.size()) {
                        map.put("strAgentLevel", channelSysBaseTypeList.get(0).getCodeName());
                        map.put("nAgentLevel", channelSysBaseTypeList.get(0).getCodeId());
                    }
                }
                ChannelIsSingle channelIsSingle = new ChannelIsSingle();
                channelIsSingle.setChannelEntityId(chnlEntId);
                List<ChannelIsSingle> channelIsSingleList = channelIsSingleDao.query(channelIsSingle);
                if (channelIsSingleList.size() != 0) {
                    map.put("channelEntityType", channelIsSingleList.get(0).getChannelEntityType());
                } else {
                    map.put("channelEntityType", 2);
                }
            }
            //网点服务号码
            if (2 == nFlag) {
                ChannelNode channelNode = new ChannelNode();
                channelNode.setNodeId(chnlEntId);
                //查询网点信息
                List<ChannelNode> channelNodeList = channelNodeDao.query(channelNode);
                if (0 == channelNodeList.size()) {
                    throw new Exception(e3);
                }
                ChannelEntityRelationInfo channelEntityRelationInfo_ = new ChannelEntityRelationInfo();
                channelEntityRelationInfo_.setChannelEntityId(chnlEntId);
                channelEntityRelationInfo_.setRelationType(ChannelConstants.RELATION_TYPE_SUPERVISION);
                //查询网点督导信息
                channelEntityRelationInfoList = channelEntityRelationInfoDao.query(channelEntityRelationInfo_);
                if (0 != channelEntityRelationInfoList.size()) {
                    map.put("cusManagerName", channelEntityRelationInfoList.get(0).getRelationName());
                    map.put("cusManagerPhone", channelEntityRelationInfoList.get(0).getRelationMobile());
                    map.put("cusManagerOtherConnect", channelEntityRelationInfoList.get(0).getOtherContact());
                }
                map.put("nFlag", "2");
                map.put("channelEntityId", chnlEntId);
                map.put("nodeType", channelNodeList.get(0).getNodeType() == null ? 0 : channelNodeList.get(0).getNodeType());
                map.put("nodeName", channelEntityBasicInfoList.get(0).getChannelEntityName());
                map.put("nodeAddr", channelNodeList.get(0).getNodeAddr());
                map.put("areaCodeID", channelEntityBasicInfoList.get(0).getDistrictId());
                ChannelSysBaseType channelSysBaseType = new ChannelSysBaseType();
                channelSysBaseType.setCodeType(ChannelConstants.BELONG_AREA_IN_BASE_TYPE);
                if (channelEntityBasicInfoList.get(0).getDistrictId() != null) {
                    logger.info(channelEntityBasicInfoList.get(0).getDistrictId().toString());
                    channelSysBaseType.setCodeId(channelEntityBasicInfoList.get(0).getDistrictId());
                    //查询渠道归属
                    List<ChannelSysBaseType> channelSysBaseTypeList = channelSysBaseTypeDao.query(channelSysBaseType);
                    map.put("areaName", channelSysBaseTypeList.get(0).getCodeName());
                }

                ChannelIsSingle channelIsSingle = new ChannelIsSingle();
                channelIsSingle.setChannelEntityId(chnlEntId);
                List<ChannelIsSingle> channelIsSingleList = channelIsSingleDao.query(channelIsSingle);
                if (channelIsSingleList.size() != 0) {
                    map.put("channelEntityType", channelIsSingleList.get(0).getChannelEntityType());
                } else {
                    map.put("channelEntityType", 2);
                }
            }
            //关于“代理查询”记录登录信息
            AgentQueryRecord agentQueryRecord = new AgentQueryRecord();
            agentQueryRecord.setChannelEntityId(chnlEntId);
            agentQueryRecord.setDoneDate(DateUtil.getCurrDate());
            agentQueryRecord.setBusiType(ChannelConstants.AGENT_QUERY_RECORD_BASE_INFO);
            agentQueryRecord.setBusiName(ChannelConstants.AGENT_QUERY_RECORD_BASE_INFO_STR);
            agentQueryRecord.setFunctionName("get_channelInfoBySvrNum");
            int isSuccess = agentQueryRecordDao.insert(agentQueryRecord);
            if (1 != isSuccess) {
                throw new Exception("代理查询记录失败");
            }
        } catch (Exception e) {
            logger.error("根据服务号码 返回代理商或网点信息失败：" + e.getMessage());
            if (e.getMessage().equals(e1)) {
                ChannelManagerSvrMobile channelManagerSvrMobile = new ChannelManagerSvrMobile();
                channelManagerSvrMobile.setMobile(svrNum);
                List<ChannelManagerSvrMobile> channelManagerSvrMobileList =
                        channelManagerSvrMobileDao.query(channelManagerSvrMobile);
                if (0 == channelManagerSvrMobileList.size()) {
                    throw new Exception("号码不是有效的服务号码，没有查到相关信息");
                }
                map.put("nFlag", 3);
                logger.debug("********    getChannelInfoBySvrNum  end  ********");
                return map;
            }
            logger.debug("********    getChannelInfoBySvrNum  end  ********");
            if (e.getMessage().endsWith(e2) || e.getMessage().equals(e3)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    getChannelInfoBySvrNum  end  ********");
        return map;
    }

    /**
     * @param : channelId 				渠道编号
     * @param : billMonth 				账单月
     * @param : opId 					操作员编号
     * @param : orgId					操作员组织编号
     * @Desc : 渠道扁平化专项工作之代理商服务管理
     * @return: yhcardAward                有号卡佣金
     * @return: dlhzjfAward                代理合作积分佣金
     * @return: cxhdAward                促销活动佣金
     * @return: dyjfAward                店员积分酬金
     * @return: yjcardAward                有价卡佣金（代码中写死为0）
     * @return: otherAward                其他佣金（代码中写死为0）
     * @return: allAward                总佣金
     */
    public Map<String, Object> get_singleAgentAward(String param) throws Exception {
        logger.debug("********    getSingleAgentAward  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        Long channelId;
        Integer billMonth;
        map.put("marker", "254");
        map.put("yhcardAward", "0");
        map.put("dlhzjfAward", "0");
        map.put("cxhdAward", "0");
        map.put("dyjfAward", "0");
        map.put("yjcardAward", "0");
        map.put("agentName", "");
        map.put("otherAward", "0");
        map.put("allAward", "0");
        map.put("extstr1", "");
        map.put("extint1", "0");
        map.put("extint2", "0");
        map.put("extint3", "0");
        map.put("extint4", "0");
        map.put("extint5", "0");
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            channelId = jsonParam.getLong("channelId");
            billMonth = jsonParam.getInt("billMonth");
        } catch (Exception e) {
            logger.error("渠道扁平化专项工作之代理商服务管理失败：" + e.getMessage());
            logger.debug("********    getSingleAgentAward  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        if (billMonth >= 209912 || billMonth < 200001) {
            throw new Exception("查询的月份超出范围!");
        }
        String e1 = "没有查询到对应的代理商信息";
        try {
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(channelId);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                    channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (0 == channelEntityBasicInfoList.size()) {
                throw new Exception(e1);
            }
            Long agentId;
            if (2 == channelEntityBasicInfoList.get(0).getChannelEntityType()) {
                ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
                channelEntityRelInfo.setChannelEntityId(channelId);
                List<ChannelEntityRelInfo> channelEntityRelInfoList =
                        channelEntityRelInfoDao.query(channelEntityRelInfo);
                agentId = channelEntityRelInfoList.get(0).getParentEntity();
            } else {
                agentId = channelId;
            }
            //有号卡佣金
            Long yhcardAward = agentFeeDtlDao.getYhcardAward(agentId, billMonth);
            map.put("yhcardAward", yhcardAward);
            //代理合作积分佣金
            Long dlhzjfAward = agentFeeDtlDao.getDlhzjfAward(agentId, billMonth);
            map.put("dlhzjfAward", dlhzjfAward);
            //促销活动佣金
            Long cxhdAward = agentFeeDtlDao.getCxhdAward(agentId, billMonth);
            map.put("cxhdAward", cxhdAward);
            //店员积分酬金
            Long dyjfAward = agentDyjfAmountDao.getDyjfAward(agentId, billMonth);
            map.put("dyjfAward", dyjfAward);
            //有价卡佣金（老系统代码中注明为0）
            map.put("yjcardAward", 0);
            //其他佣金（老系统代码中注明为0）
            map.put("otherAward", 0);
            //佣金总计
            Long allAward = yhcardAward + dlhzjfAward + cxhdAward + dyjfAward;
            map.put("allAward", allAward);
        } catch (Exception e) {
            logger.error("渠道扁平化专项工作之代理商服务管理失败：" + e.getMessage());
            logger.debug("********    getSingleAgentAward  end  ********");
            //			if(e.getMessage().equals(e1)){
            //				throw e;
            //			}
            //			throw new Exception("查询失败");
        }
        logger.debug("********    getSingleAgentAward  end  ********");
        return map;
    }

    /**
     * @param : channelId 				渠道编号
     * @param : billMonth 				账单月
     * @param : opId 					操作员编号
     * @param : orgId					操作员组织编号
     * @Desc : 代理商酬金支付进度表 —>查询
     * @return: agentId                    代理商ID
     * @return: agentName                代理商名称
     * @return: rewardSubjects            酬金科目
     * @return: rewardAmount            酬金金额
     * @return: rewardMonth                酬金月份
     * @return: paymentTime                付款时间
     * @return: rewardPayer                收款人名称
     * @return: remarks                    备注
     * @return: insertTime                导入数据的时间
     * @return: ext1                    1-有效 0-无效
     * @return: ext2                    ext2
     * @return: ext3                    供应商账号
     * @return: ext4                    操作时间
     * @return: id                         序号
     * @return: programe                报账栏目
     * @return: agentBank                开户银行
     * @return: agentSubBank             开户银行支行
     * @return: imtTempTime                导出纳平台时间
     * @return: checkTime                提交审核时间
     * @return: recStatus                支付状态
     */
    public List<JSONObject> get_RewardPaymentSchedule(String param) throws Exception {
        logger.debug("********    getRewardPaymentSchedule  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        List<JSONObject> reJsonList = new ArrayList<JSONObject>();
        Long channelId;
        Long billMonth;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            billMonth = jsonParam.getLong("billMonth");
        } catch (Exception e) {
            logger.error("代理商酬金支付进度表 —>查询失败：" + e.getMessage());
            logger.debug("********    getRewardPaymentSchedule  end  ********");
            throw new Exception("查询的月份超过范围！");
        }
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            channelId = jsonParam.getLong("channelId");
        } catch (Exception e) {
            logger.error("代理商酬金支付进度表 —>查询失败：" + e.getMessage());
            logger.debug("********    getRewardPaymentSchedule  end  ********");
            throw new Exception("错误的渠道实体类型，请确认输入的是代理商或者网点的渠道实体类型编号!");
        }

        if (billMonth > Integer.parseInt(DateUtil.formatCurrentDate("yyyyMM")) || billMonth < 200001) {
            throw new Exception("查询的月份超过范围！");
        }
        String e1 = "错误的渠道实体类型，请确认输入的是代理商或者网点的渠道实体类型编号 !";
        String e2 = "错误的渠道实体类型，请确认输入的是代理商或者网点的渠道实体类型编号";
        String e3 = "没有查询到代理商信息！";
        String e4 = "酬金正在审核中，请耐心等待";
        try {
            String channelEntityName = "";
            String fullName = "";
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            ChannelAgentInfo channelAgentInfo = new ChannelAgentInfo();
            channelEntityBasicInfo.setChannelEntityId(channelId);
            //根据渠道实体编号查询渠道实体类型
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                    channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (0 == channelEntityBasicInfoList.size()) {
                throw new Exception(e1);
            }
            Integer channelEntityType = channelEntityBasicInfoList.get(0).getChannelEntityType();
            if (ChannelConstants.CHANNEL_ENTITY_TYPE_AGENT != channelEntityType &&
                    ChannelConstants.CHANNEL_ENTITY_TYPE_NODE != channelEntityType) {
                throw new Exception(e2);
            }
            //代理商的情况下获得代理商名称
            if (ChannelConstants.CHANNEL_ENTITY_TYPE_AGENT == channelEntityType) {
                //代理商简称
                channelEntityName = channelEntityBasicInfoList.get(0).getChannelEntityName();
                channelAgentInfo.setAgentId(channelId);
                List<ChannelAgentInfo> channelAgentInfoList = channelAgentInfoDao.query(channelAgentInfo);
                if (0 == channelAgentInfoList.size()) {
                    throw new Exception(e3);
                }
                //代理商全称
                fullName = channelAgentInfoList.get(0).getFullName();
            }
            //网点的情况下获得代理商名称
            if (ChannelConstants.CHANNEL_ENTITY_TYPE_NODE == channelEntityType) {
                ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
                channelEntityRelInfo.setChannelEntityId(channelId);
                //查询网点所属的代理商编号
                List<ChannelEntityRelInfo> channelEntityRelInfoist =
                        channelEntityRelInfoDao.query(channelEntityRelInfo);
                Long parent = channelEntityRelInfoist.get(0).getParentEntity();
                channelEntityBasicInfo.setChannelEntityId(parent);
                //代理商简称
                List<ChannelEntityBasicInfo> channelEntityBasicInfoList_ =
                        channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                channelEntityName = channelEntityBasicInfoList.get(0).getChannelEntityName();
                channelAgentInfo.setAgentId(parent);
                //代理商全称
                List<ChannelAgentInfo> channelAgentInfoList = channelAgentInfoDao.query(channelAgentInfo);
                if (0 == channelAgentInfoList.size()) {
                    throw new Exception(e3);
                }
                fullName = channelAgentInfoList.get(0).getFullName();
            }
            if (Long.parseLong(DateUtil.formatCurrentDate("yyyyMM")) == billMonth) {
                map.put("msg", "can not find with condition is: (agent_name = '" + fullName + "' or agent_name='" + channelEntityName + "') and reward_month =201411");
                map.put("hint", "查询酬金明细失败！");
                JSONObject re = JSONObject.fromObject(map);
                reJsonList.add(re);
            }
            //获得代理商酬金支付进度
            List<RewardPaymentSchedule> rewardPaymentScheduleList =
                    rewardPaymentScheduleDao.getRewardPaymentSchedule(channelEntityName, fullName, billMonth);
            if (0 == rewardPaymentScheduleList.size()) {
                //网点情况下，查询酬金明细表
                if (ChannelConstants.CHANNEL_ENTITY_TYPE_NODE == channelEntityType) {
                    AgentBusiAmount agentBusiAmount = new AgentBusiAmount();
                    agentBusiAmount.setNodeId(channelId);
                    agentBusiAmount.setBillMonth(billMonth);
                    //查询网点酬金明细表
                    List<AgentBusiAmount> agentBusiAmountList = agentBusiAmountDao.query(agentBusiAmount);
                    if (0 != agentBusiAmountList.size()) {
                        throw new Exception(e4);
                    }
                } else if (ChannelConstants.CHANNEL_ENTITY_TYPE_AGENT == channelEntityType) {//代理商情况下，查询酬金明细表
                    ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
                    channelEntityRelInfo.setParentEntity(channelId);
                    //查询代理商下属网点
                    List<ChannelEntityRelInfo> channelEntityRelInfoList =
                            channelEntityRelInfoDao.query(channelEntityRelInfo);
                    for (ChannelEntityRelInfo ceri : channelEntityRelInfoList) {
                        Long nodeId = ceri.getChannelEntityId();
                        AgentBusiAmount agentBusiAmount = new AgentBusiAmount();
                        agentBusiAmount.setNodeId(nodeId);
                        //查询网点酬金明细表
                        List<AgentBusiAmount> agentBusiAmountList = agentBusiAmountDao.query(agentBusiAmount);
                        if (0 != agentBusiAmountList.size()) {
                            throw new Exception(e4);
                        }
                    }
                }
            } else {
                for (RewardPaymentSchedule rps : rewardPaymentScheduleList) {
                    JSONObject reJson = JSONObject.fromObject(rps);
                    //转换返回参数中的日期格式
                    if (!reJson.getString("paymentTime").equals("null") && !reJson.getString("paymentTime").equals("")) {
                        reJson = this.changeTime(reJson, "paymentTime");
                    } else {
                        reJson.put("paymentTime", "");
                    }

                    if (!reJson.getString("insertTime").equals("null") && !reJson.getString("insertTime").equals("")) {
                        reJson = this.changeTime(reJson, "insertTime");
                    } else {
                        reJson.put("insertTime", "");
                    }

                    if (!reJson.getString("imtTempTime").equals("null") && !reJson.getString("imtTempTime").equals("")) {
                        reJson = this.changeTime(reJson, "imtTempTime");
                    } else {
                        reJson.put("imtTempTime", "");
                    }

                    if (!reJson.getString("checkTime").equals("null") && !reJson.getString("checkTime").equals("")) {
                        reJson = this.changeTime(reJson, "checkTime");
                    } else {
                        reJson.put("checkTime", "");
                    }
                    reJsonList.add(reJson);
                }
            }
            //关于“代理查询”记录登录信息
            AgentQueryRecord agentQueryRecord = new AgentQueryRecord();
            agentQueryRecord.setChannelEntityId(channelId);
            agentQueryRecord.setDoneDate(DateUtil.getCurrDate());
            agentQueryRecord.setBusiType(ChannelConstants.AGENT_QUERY_RECORD_PAYMENT_SCHEDULE);
            agentQueryRecord.setBusiName(ChannelConstants.AGENT_QUERY_RECORD_PAYMENT_SCHEDULE_STR);
            agentQueryRecord.setFunctionName("get_RewardPaymentSchedule");
            agentQueryRecordDao.insert(agentQueryRecord);
        } catch (Exception e) {
            logger.error("代理商酬金支付进度表 —>查询失败：" + e.getMessage());
            logger.debug("********    getRewardPaymentSchedule  end  ********");
            if (e.getMessage().equals(e1) || e.getMessage().equals(e2)
                    || e.getMessage().equals(e3) || e.getMessage().equals(e4)) {
                throw e;
            }
        }
        logger.debug("********    getRewardPaymentSchedule  end  ********");
        return reJsonList;
    }

    /**
     * @param : channelId 				渠道编号
     * @param : orderId 				订单编号
     * @param : orderStatus 			订单状态
     * @param : beginDate 				订单分配起始日期
     * @param : endDate 				订单分配结束日期
     * @param : codeId	 				卡资源编号
     * @param : opId 					操作员编号
     * @param : orgId					操作员组织编号
     * @Desc : 获得物流接口表数据
     * @return: invoiceNo                订单号
     * @return: memberNo                EMS条码号
     * @return: memberName                订单联系人
     * @return: subDcCode                DC代码
     * @return: yesNo                    是否
     * @return: deliveryStatus            配送结果
     * @return: cartonNo
     * @return: agentId                    投递员工号
     * @return: payableAmount            本次应收款
     * @return: deliveryDate            配送完成时间
     * @return: zipcode                    邮编
     * @return: city                    银行账号(没查到账号则填送货城市)
     * @return: deliveryAddress            订单联系人地址
     * @return: phone                    订单联系电话
     * @return: newmember
     * @return: postage                    发送费
     * @return: payableAmount            配送完成应收款
     * @return: actualCollection        配送完成应收款
     * @return: discrepancyReason        DC投递点
     * @return: returnReason            退货原因
     * @return: cartonid                0为现金，*为POS
     * @return: receivedDate            导入日期
     * @return: lossDate                丢失日期
     * @return: assignedDate            派送日期
     * @return: returnedToWhseDate
     * @return: returnDate                退货日期
     * @return: recStatus                记录状态
     * @return: status
     * @return: ext1
     * @return: ext2                    白卡起始号和终止号
     * @return: ext3
     * @return: doneDate
     * @return: cardNum                    白卡数量
     * @return: entityName                代理商全称
     */
    public List<JSONObject> getOrderDelivery(String param) throws Exception {
        logger.debug("********    getOrderDelivery  begin  ********");
        Long channelId;
        Long orderId;
        Integer orderStatus;
        String beginDate;
        String endDate;
        Long resCode;
        List<JSONObject> list = new ArrayList<JSONObject>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            channelId = jsonParam.getLong("channelId");
            orderId = jsonParam.getLong("orderId");
            orderStatus = jsonParam.getInt("orderStatus");
            beginDate = jsonParam.getString("beginDate");
            endDate = jsonParam.getString("endDate");
        } catch (Exception e) {
            logger.error("获得物流接口表数据失败：" + e.getMessage());
            logger.debug("********    getOrderDelivery  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            resCode = jsonParam.getLong("resCode");
        } catch (Exception e) {
            logger.error("渠道允许自动分配的卡资源没有配置！" + e.getMessage());
            logger.debug("********    getOrderDelivery  end  ********");
            throw new Exception("渠道允许自动分配的卡资源没有配置！");
        }

        String e1 = "渠道允许自动分配的卡资源没有配置！";
        String e2 = "渠道允许自动分配的卡资源重复！";
        try {
            //			ChannelSysBaseType channelSysBaseType = new ChannelSysBaseType();
            //			channelSysBaseType.setCodeType(ChannelConstants.CARD_TYPE_IN_BASE_TYPE);
            //			channelSysBaseType.setCodeId(resCode);
            //查询卡资源是否已经配置
            List<ChannelSysBaseType> channelSysBaseTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.CARD_TYPE_IN_BASE_TYPE, Integer.valueOf("" + resCode), null, null);//channelSysBaseTypeDao.query(channelSysBaseType);
            if (0 == channelSysBaseTypeList.size()) {
                throw new Exception(e1);
            }
            if (channelSysBaseTypeList.size() > 1) {
                throw new Exception(e2);
            }
        } catch (Exception e) {
            logger.error("获得物流接口表数据失败：" + e.getMessage());
            logger.debug("********    getOrderDelivery  end  ********");
            if (e.getMessage().equals(e1) || e.getMessage().equals(e2)) {
                throw e;
            }
            throw new Exception("查询失败");
        }

        String e3 = "订单物流接口表没有相应的数据";
        List<ChannelOrderDelivery> channelOrderDeliveryList = null;
        //查询订单物流接口表
        try {
            //如果订单编号为-1，表示查询全部
            if (-1 != orderId) {
                if (-1 != channelId) {
                    channelOrderDeliveryList = channelOrderDeliveryDao.queryByCondition(orderId, orderStatus, channelId, null, null);
                } else {
                    channelOrderDeliveryList = channelOrderDeliveryDao.queryByCondition(orderId, orderStatus, null, null, null);
                }
            } else {
                beginDate = beginDate.substring(0, 10);
                endDate = endDate.substring(0, 10);

				/*try{
                    beginDate = beginDate.replaceAll('-','');
                    endDate = endDate.replaceAll('-','');
					Long.parseLong(beginDate);
					Long.parseLong(endDate);
				}catch(Exception e){
					Map<String, String> map = new HashMap<String, String>();
					List<JSONObject> reJsonList = new ArrayList<JSONObject>();
					map.put("msg", "select  invoice_no,  member_no,  member_name,  sub_dc_code,  yes_no,  delivery_status,  carton_no,  agent_id,  payable_amount,  delivery_date,  zipcode,  city,  delivery_address,  phone,  newmember,  postage,  actual_collection,  discrepancy_reason,  return_reason,  cartonid,  received_date,  loss_date,  assigned_date,  returned_to_whse_date,  return_date,  rec_status,  status,  ext1,  ext2,  ext3,  done_date,  cardnum,  entityname  from CHANNEL_ORDER_DELIVERY where INVOICE_NO in (select channel_order_id from channel_order where order_status = 6 and rec_status=1 and channel_order_source=23 and to_date(assign_date,'YYYY/MM/DD') >= to_date('"+beginDate+"', 'YYYY/MM/DD') and  to_date(assign_date,'YYYY/MM/DD') <= (1 +  to_date('"+endDate+"', 'YYYY/MM/DD')) and channel_order_id in (select channel_order_id from channel_order_pay where order_amount>0))");
					map.put("hint", "查询订单物流接口表出错");
					map.put("code", "-2");
					reJsonList.add(JSONObject.fromObject(map));
					return reJsonList;
				}*/
                if (-1 != channelId) {
                    channelOrderDeliveryList = channelOrderDeliveryDao.queryByCondition(null, orderStatus, channelId, beginDate, endDate);
                } else {
                    channelOrderDeliveryList = channelOrderDeliveryDao.queryByCondition(null, orderStatus, null, beginDate, endDate);
                }
            }
            if (0 == channelOrderDeliveryList.size()) {
                throw new Exception(e3);
            }
            for (ChannelOrderDelivery channelOrderDelivery : channelOrderDeliveryList) {
                ResAssignOrderDtl resAssignOrderDtl = new ResAssignOrderDtl();
                resAssignOrderDtl.setResCode(resCode);
                resAssignOrderDtl.setOrderId(channelOrderDelivery.getInvoiceNo());

                //查找起始号和终止号
                List<ResAssignOrderDtl> resAssignOrderDtlList = resAssignOrderDtlDao.queryOrderByBegid(resAssignOrderDtl);
                int size = resAssignOrderDtlList.size();
                //-----新增-------三行
                if (0 == size) {
                    continue;
                }

                ChannelSysBaseType _channelSysBaseType = new ChannelSysBaseType();
                _channelSysBaseType.setCodeType(ChannelConstants.CARD_TYPE_IN_BASE_TYPE);
                _channelSysBaseType.setExt2(ChannelConstants.CARD_TYPE_OF_SIM);
                List<ChannelSysBaseType> _channelSysBaseTypeList = channelSysBaseTypeDao.query(_channelSysBaseType);
                List<Integer> cardType = new ArrayList<Integer>();
                for (ChannelSysBaseType c : _channelSysBaseTypeList) {
                    cardType.add(c.getCodeId());
                }
                if (0 < resAssignOrderDtlList.size()) {
                    //					if(ChannelConstants.CHOOSEPHONE_AIR_WHILTE_CARD==resCode|| ChannelConstants.CHOOSEPHONE_AIR_WHILTE_CARD_CHEAP==resCode
                    //							|| ChannelConstants.CHOOSEPHONE_AIR_WHILTE_CARD_SYS==resCode || ChannelConstants.LTE_WRITE_MICRO_USIM==resCode
                    //							|| ChannelConstants.LTE_WRITE_NANO_USIM==resCode || ChannelConstants.M_ZONE_128K_WRITE_CARD==resCode
                    //							|| ChannelConstants.GSM_128K_WRITE_CARD ==resCode|| ChannelConstants.LTE_BUSINESS_USIM==resCode||
                    //							ChannelConstants.MICRO_USIM==resCode||ChannelConstants.NANO_USIM==resCode){
                    if (cardType.contains(Integer.valueOf("" + resCode))) {
                        //查询白卡数量
                        List<SimCardNo> simCardNoList = simCardNoDao.getSimCard(resAssignOrderDtlList.get(0).getBegId(), resAssignOrderDtlList.get(size - 1).getBegId(), resCode, channelOrderDelivery.getInvoiceNo());
                        String strWhiteCardBegEnd = "";
                        Long cardNum = 0l;
                        for (int i = 0; i < simCardNoList.size(); i++) {
                            if (0 == i) {
                                strWhiteCardBegEnd = simCardNoList.get(i).getStartWhiteNo() + "-" + simCardNoList.get(i).getEndWhiteNo();
                            } else {
                                strWhiteCardBegEnd = strWhiteCardBegEnd + "," + simCardNoList.get(i).getStartWhiteNo() + "-" + simCardNoList.get(i).getEndWhiteNo();
                            }
                            cardNum += simCardNoList.get(i).getCardNum();
                        }
                        channelOrderDelivery.setExt2(strWhiteCardBegEnd);
                        channelOrderDelivery.setCardnum(cardNum);
                        //						if(0 < simCardNoList.size()){
                        //							String strWhiteCardBegEnd = simCardNoList.get(0).getStartWhiteNo() + "-" + simCardNoList.get(0).getEndWhiteNo();
                        //							channelOrderDelivery.setExt2(strWhiteCardBegEnd);
                        //							channelOrderDelivery.setCardnum(simCardNoList.get(0).getCardNum());
                        //						}
                        //获得物流接口相关数据
//						channelOrderDelivery = this.getChannelOrderDeliveryExt(channelOrderDelivery,channelOrderDeliveryList);
                        channelOrderDelivery = this.getChannelOrderDeliveryExt(channelOrderDelivery);
                    } else {
                        //单店充值卡序列号
                        String WhiteCardBegEnd = resAssignOrderDtlList.get(0).getBegId() + "-" + resAssignOrderDtlList.get(0).getBegId();
                        channelOrderDelivery.setExt2(WhiteCardBegEnd);
                        channelOrderDelivery.setCardnum(resAssignOrderDtlList.get(0).getAmount());
                        //获得物流接口相关数据
//						channelOrderDelivery = this.getChannelOrderDeliveryExt(channelOrderDelivery,channelOrderDeliveryList);
                        channelOrderDelivery = this.getChannelOrderDeliveryExt(channelOrderDelivery);

                        //						ChannelSysBaseType channelSysBaseType = new ChannelSysBaseType();
                        //						channelSysBaseType.setCodeType(ChannelConstants.CARD_TYPE_IN_BASE_TYPE);
                        //						channelSysBaseType.setCodeId(resCode);

                        //						实际传的Newmember都是空，虽然代码里有写，按实际的来
                        //						List<ChannelSysBaseType> channelSysBaseTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.CARD_TYPE_IN_BASE_TYPE, Integer.valueOf(""+resCode), null, null);//channelSysBaseTypeDao.query(channelSysBaseType);
                        //						channelOrderDelivery.setNewmember(channelSysBaseTypeList.get(0).getCodeName());
                    }
                }
                JSONObject reJson = JSONObject.fromObject(channelOrderDelivery);
                //转换返回参数中的日期格式
                if (!reJson.getString("doneDate").equals("null") && !reJson.getString("doneDate").equals("")) {
                    reJson = this.changeTime(reJson, "doneDate");
                } else {
                    reJson.put("doneDate", "");
                }
                if (reJson.getString("actualCollection").equals("null")) {
                    reJson.put("actualCollection", "");
                }
                if (reJson.getString("cartonid").equals("null")) {
                    reJson.put("cartonid", "");
                }
                if (reJson.getString("returnReason").equals("null")) {
                    reJson.put("returnReason", "");
                }
                if (reJson.getString("discrepancyReason").equals("null")) {
                    reJson.put("discrepancyReason", "");
                }

                list.add(reJson);
            }
        } catch (Exception e) {
            logger.error("查询订单物流接口表出错" + e.getMessage());
            logger.debug("********    getOrderDelivery  end  ********");
            if (e.getMessage().equals(e3)) {
                throw e;
            }
            throw new Exception("查询订单物流接口表出错");
        }
        logger.debug("********    getOrderDelivery  end  ********");
        return list;
    }

    /**
     * @param : channelId 				渠道编号
     * @param : billMonth 				账单月
     * @param : opId 					操作员编号
     * @param : orgId					操作员组织编号
     * @Desc : 优化社会渠道销售系统代理查询功能
     * @return: totalFeeTFK                套封卡佣金
     * @return: totalFeeYHK                有号卡佣金
     * @return: totalFeeYJK                有价卡佣金（经分确认为0）
     * @return: totalFeeHZJF            终端活动佣金
     * @return: totalFeeCXHD            促销活动佣金
     * @return: totalFeeKH                考核酬金
     * @return: totalFeeDYJF            店员积分酬金
     * @return: totalFeeXTYW            系统业务酬金
     * @return: totalFeeZLKH            质量考核
     * @return: totalFeeZWCJ            6个月在网酬金
     * @return: totalFeeXRWCJ            新入网充值活动
     * @return: totalFeeXJKH            星级网点考核酬金
     * @return: totalFeeZGDYW            直供店业务酬金
     * @return: totalFeeKHCJ            卡号酬金调整费
     * @return: totalFeeOTHER            其他 0 经分确认
     * @return: totalFeeLKCJ            领卡酬金 0
     * @return: totalFeeCOUNT            佣金合计
     * @return: totalFeeCCF                差错费
     */
    public Map<String, Object> get_singleAgentAwardExt(String param) throws Exception {
        logger.debug("********    getSingleAgentAwardExt  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        Long channelId;
        Integer billMonth;
        List<Long> nodeIdList = new ArrayList<Long>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            channelId = jsonParam.getLong("channelId");
            billMonth = jsonParam.getInt("billMonth");
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
            logger.debug("********    getSingleAgentAwardExt  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        //校验输入月份
        //		int year =  billMonth/100;
        //		int month = billMonth%100;
        if (billMonth >= 209912 || billMonth < 200001) {
            throw new Exception("查询的月份超出范围");
        }
        int iFlag = -1;                                        //如果是代理商id，那么将iFlag置为0，否则将iFlag置为1,同时将该单点商的下属网点查询出来
        try {
            //渠道侧根据传递过来的lChannelId判断是代理商id还是网点id
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(channelId);
            channelEntityBasicInfo.setChannelEntityType(ChannelConstants.CHANNEL_ENTITY_TYPE_AGENT);
            Integer[] channelEntityStatuss = {ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT, ChannelConstants.CHANNEL_ENTITY_STATUS_LOGOUT};
            channelEntityBasicInfo.setChannelEntityStatuss(channelEntityStatuss);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (0 != channelEntityBasicInfoList.size()) {
                //是代理商
                iFlag = 0;
            } else {
                //是网点
                iFlag = 1;
            }
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
            logger.debug("********    getSingleAgentAwardExt  end  ********");
            throw new Exception("根据channelID查询，判断是代理商id还是网点id，数据库异常");
        }
        if (0 == iFlag) {
            //根据代理商id查找到该单点商的类型(等级)、代理商姓名、所属分公司、身份证号码信息
            try {
                List<AgentAccInfo> agentAccInfoList = agentAccInfoDao.queryByChannelId(channelId);
                map.put("agentName", agentAccInfoList.get(0).getAgentName());
                map.put("agentBranch", agentAccInfoList.get(0).getBranch());
                map.put("idCard", agentAccInfoList.get(0).getIdCard());
                ChannelAgentTypeInfo channelAgentTypeInfo = new ChannelAgentTypeInfo();
                channelAgentTypeInfo.setAgentId(channelId);
                channelAgentTypeInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                List<ChannelAgentTypeInfo> channelAgentTypeInfoList = channelAgentTypeInfoDao.query(channelAgentTypeInfo);
                if (0 != channelAgentTypeInfoList.size()) {
                    map.put("strAgentType", channelAgentTypeInfoList.get(0).getAgentType());
                }
                map.put("strAgentType", "0");
            } catch (Exception e) {
                logger.error("错误原因" + e.getMessage());
                logger.debug("********    getSingleAgentAwardExt  end  ********");
                throw new Exception("查询代理商姓名，身份证信息等出错");
            }
            //根据代理商id查找到该单点商下属的所有的网点id
            try {
                ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
                channelEntityRelInfo.setParentEntity(channelId);
                channelEntityRelInfo.setChannelEntityType(ChannelConstants.CHANNEL_ENTITY_TYPE_NODE);
                List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.query(channelEntityRelInfo);
                for (ChannelEntityRelInfo crei : channelEntityRelInfoList) {
                    Long a = crei.getChannelEntityId();
                    nodeIdList.add(a);
                }
                nodeIdList = this.removeDuplicate(nodeIdList);
                System.out.println(nodeIdList);
            } catch (Exception e) {
                logger.error("错误原因" + e.getMessage());
                logger.debug("********    getSingleAgentAwardExt  end  ********");
                throw new Exception("根据代理商编号查询下属网点出错");
            }
        } else {
            //网点情况下
            //查询网点对应的代理商
            try {
                ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
                channelEntityRelInfo.setChannelEntityId(channelId);
                channelEntityRelInfo.setChannelEntityType(ChannelConstants.CHANNEL_ENTITY_TYPE_NODE);
                List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.query(channelEntityRelInfo);
                channelId = channelEntityRelInfoList.get(0).getParentEntity();
            } catch (Exception e) {
                logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
                logger.debug("********    getSingleAgentAwardExt  end  ********");
                throw new Exception("入参网点ID错误，或者没有对应的代理商ID !");
            }
            //根据代理商id查找到该单点商的类型(等级)、代理商姓名、所属分公司、身份证号码信息
            try {
                List<AgentAccInfo> agentAccInfoList = agentAccInfoDao.queryByChannelId(channelId);
                //				map.put("strAgentType", agentAccInfoList.get(0).getAgentLevel());
                map.put("agentName", agentAccInfoList.get(0).getAgentName());
                map.put("agentBranch", agentAccInfoList.get(0).getBranch());
                map.put("idCard", agentAccInfoList.get(0).getIdCard());

                ChannelAgentTypeInfo channelAgentTypeInfo = new ChannelAgentTypeInfo();
                channelAgentTypeInfo.setAgentId(channelId);
                channelAgentTypeInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                List<ChannelAgentTypeInfo> channelAgentTypeInfoList = channelAgentTypeInfoDao.query(channelAgentTypeInfo);
                if (0 != channelAgentTypeInfoList.size()) {
                    map.put("strAgentType", channelAgentTypeInfoList.get(0).getAgentType());
                }
                map.put("strAgentType", "0");
            } catch (Exception e) {
                logger.error("错误原因" + e.getMessage());
                logger.debug("********    getSingleAgentAwardExt  end  ********");
                throw new Exception("查询代理商姓名，身份证信息等出错");
            }
            //根据代理商id查找到该单点商下属的所有的网点id
            try {
                ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
                channelEntityRelInfo.setParentEntity(channelId);
                channelEntityRelInfo.setChannelEntityType(ChannelConstants.CHANNEL_ENTITY_TYPE_NODE);
                List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.query(channelEntityRelInfo);
                for (ChannelEntityRelInfo crei : channelEntityRelInfoList) {
                    Long a = crei.getChannelEntityId();
                    nodeIdList.add(a);
                }
                nodeIdList = this.removeDuplicate(nodeIdList);
                System.out.println(nodeIdList);
            } catch (Exception e) {
                logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
                logger.debug("********    getSingleAgentAwardExt  end  ********");
                throw new Exception("入参网点ID错误，或者没有对应的代理商ID !");
            }
        }
        logger.debug("iFlag:" + iFlag);
        //开始酬金查询
        logger.debug("-----------------------开始酬金查询----------------------------");
        Long totalFeeTFK = 0L;                        //1、套封卡佣金
        Long totalFeeYHK = 0L;                        //2、有号卡佣金
        Long totalFeeYJK = 0L;                        //3、有价卡佣金（经分确认为0）
        Long totalFeeHZJF = 0L;                        //4、代理合作积分佣金
        Long totalFeeZDHD = 0L;                        //5、终端活动佣金
        Long totalFeeCXHD = 0L;                        //6、促销活动佣金
        Long totalFeeKH = 0L;                        //7、考核酬金
        Long totalFeeDYJF = 0L;                        //8、店员积分酬金
        Long totalFeeXTYW = 0L;                        //9、系统业务酬金
        Long totalFeeZLKH = 0L;                        //10、质量考核
        Long totalFeeZWCJ = 0L;                        //11、6个月在网酬金
        Long totalFeeXRWCJ = 0L;                    //12、新入网充值活动
        Long totalFeeXJKH = 0L;                        //13、星级网点考核酬金
        Long totalFeeZGDYW = 0L;                    //14、直供店业务酬金
        Long totalFeeKHCJ = 0L;                        //15、卡号酬金调整费
        Long totalFeeOTHER = 0L;                    //16、其他 0 经分确认
        Long totalFeeLKCJ = 0L;                        //17、领卡酬金 0
        Long totalFeeCOUNT = 0L;                    //18、佣金合计
        Long totalFeeCCF = 0L;                        //19、差错费
        //1、套封卡佣金
        try {
            totalFeeTFK = agentFeeDtlDao.getTfkAward(channelId, billMonth);
            map.put("totalFeeTFK", totalFeeTFK);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //2、有号卡佣金
        try {
            totalFeeYHK = agentFeeDtlDao.getYhkAward(channelId, billMonth);
            map.put("totalFeeYHK", totalFeeYHK);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //3、有价卡佣金（经分确认为0）
        map.put("totalFeeYJK", totalFeeYJK);
        //4、代理合作积分佣金
        try {
            totalFeeHZJF = agentFeeDtlDao.getHzjfAward(channelId, billMonth);
            map.put("totalFeeHZJF", totalFeeHZJF);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //5、终端活动佣金
        try {
            totalFeeZDHD = agentFeeDtlDao.getZdhdAward(channelId, billMonth);
            map.put("totalFeeZDHD", totalFeeZDHD);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //6、促销活动佣金
        try {
            totalFeeCXHD = agentFeeDtlDao.getCxhdAward(channelId, billMonth);
            map.put("totalFeeCXHD", totalFeeCXHD);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //7、考核酬金
        try {
            totalFeeKH = agentFeeDtlDao.getKhAward(channelId, billMonth);
            map.put("totalFeeKH", totalFeeKH);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //8、店员积分酬金
        try {
            totalFeeDYJF = agentFeeDtlDao.getDyjfAward(channelId, billMonth);
            map.put("totalFeeDYJF", totalFeeDYJF);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //9、系统业务酬金
        try {
            totalFeeXTYW = agentFeeDtlDao.getXtywAward(channelId, billMonth);
            map.put("totalFeeXTYW", totalFeeXTYW);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //10、质量考核
        try {
            totalFeeZLKH = agentFeeDtlDao.getZlkhAward(channelId, billMonth);
            map.put("totalFeeZLKH", totalFeeZLKH);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //11、6个月在网酬金
        try {
            totalFeeZWCJ = agentFeeDtlDao.getZwcjAward(channelId, billMonth);
            map.put("totalFeeZWCJ", totalFeeZWCJ);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //12、新入网充值活动
        try {
            totalFeeXRWCJ = agentFeeDtlDao.getXrwcjAward(channelId, billMonth);
            map.put("totalFeeXRWCJ", totalFeeXRWCJ);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //13、星级网点考核酬金
        try {
            totalFeeXJKH = agentFeeDtlDao.getXjkhAward(channelId, billMonth);
            map.put("totalFeeXJKH", totalFeeXJKH);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //14、直供店业务酬金
        try {
            totalFeeZGDYW = agentFeeDtlDao.getZgdywAward(channelId, billMonth);
            map.put("totalFeeZGDYW", totalFeeZGDYW);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //15、卡号酬金调整费
        try {
            totalFeeKHCJ = agentFeeDtlDao.getKhcjAward(channelId, billMonth);
            map.put("totalFeeKHCJ", totalFeeKHCJ);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
        }
        //16、其他 0 经分确认
        map.put("totalFeeOTHER", totalFeeOTHER);
        //17、领卡酬金 0
        map.put("totalFeeLKCJ", totalFeeLKCJ);
        //18、佣金合计
        totalFeeCOUNT = totalFeeTFK + totalFeeYHK + totalFeeYJK + totalFeeHZJF + totalFeeZDHD;
        totalFeeCOUNT += totalFeeCXHD + totalFeeKH + totalFeeDYJF + totalFeeXTYW + totalFeeZLKH;
        totalFeeCOUNT += totalFeeZWCJ + totalFeeXRWCJ + totalFeeXJKH + totalFeeZGDYW + totalFeeKHCJ;
        totalFeeCOUNT += totalFeeOTHER + totalFeeLKCJ;
        map.put("totalFeeCOUNT", totalFeeCOUNT);
        //19、差错费
        map.put("totalFeeCCF", totalFeeCCF);
        //关于“代理查询”记录登录信息
        try {
            AgentQueryRecord agentQueryRecord = new AgentQueryRecord();
            agentQueryRecord.setChannelEntityId(channelId);
            agentQueryRecord.setDoneDate(DateUtil.getCurrDate());
            agentQueryRecord.setBusiType(ChannelConstants.AGENT_QUERY_RECORD_SVR_FEE);
            agentQueryRecord.setBusiName(ChannelConstants.AGENT_QUERY_RECORD_SVR_FEE_STR);
            agentQueryRecord.setFunctionName("get_singleAgentAwardExt");
            int isSuccess = agentQueryRecordDao.insert(agentQueryRecord);
        } catch (Exception e) {
            logger.error("优化社会渠道销售系统代理查询功能失败：" + e.getMessage());
            logger.debug("********    getSingleAgentAwardExt  end  ********");
        }
        logger.debug("********    getSingleAgentAwardExt  end  ********");
        map.put("m_llGrsds", 0L);//个人所得税（代扣代缴个人所得税）
        map.put("m_strNotes", "");
        map.put("m_llGrsd", 0L);//个人实得
        map.put("m_iTypeID", 0);//税金类型
        map.put("m_llNsjs", 0L);//个人所得税纳税基数
        map.put("m_llExtint6", 0L);
        map.put("m_llExtint5", 0L);
        map.put("m_llExtint4", 0L);
        map.put("m_strExtstr3", "");
        map.put("m_strExtstr2", "");
        map.put("m_strExtstr1", "");
        map.put("m_llYysj", 0L);//营业税金及附加（代扣代缴个人所得税）
        map.put("m_llMarker", 2909657911879033196L);
        return map;
    }

    /**
     * @param : svrNum 						服务号码
     * @Desc : 根据手机号码查询代理商或网点信息
     * @return: m_nRecStatus                记录状态
     * @return: m_iChannelEntityId            渠道编号
     * @return: m_dateDoneDate                操作时间
     * @return: m_strChannelEntitySerial    渠道序列号
     * @return: m_iDistrictId                归属区域
     * @return: m_nChannelEntityType        渠道类型  老接口：1 代理商（非单点商） 2网点（非单点商的下属网点）3 代理商：单点商  4 网点 单点商下属网点
     * @return: m_llDoneCode                操作编码
     * @return: m_strChannelEntityName        渠道名称
     * @return: m_iOrgId                    操作组织
     * @return: m_iOpId                        操作员
     * @return: m_nIsNetwork                是否联网
     * @return: m_nIsExclusive                是否排他
     * @return: m_strRegMobile                注册号码
     * @return: m_nChannelEntityStatus        渠道状态
     * @return: m_strExt1
     * @return: m_iExt2
     * @return: m_strExt3
     */
    public Map<String, Object> get_channelInfoByMobileNum(String param) throws Exception {
        logger.debug("********    get_channelInfoByMobileNum  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String svrNum;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            svrNum = jsonParam.getString("svrNum");
        } catch (Exception e) {
            logger.error("根据手机号码查询代理商或网点信息:" + e.getMessage());
            logger.debug("********    get_channelInfoByMobileNum  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        if (11 != svrNum.length()) {
            throw new Exception("输入手机号码长度不正确!");
        }

        String e1 = " 根据手机号查询渠道编号:存在多条记录，主键冲突";
        //根据服务号码，获取对应的渠道ID
        try {
            ChannelEntityRelationInfo channelEntityRelationInfo = new ChannelEntityRelationInfo();
            channelEntityRelationInfo.setRelationMobile(svrNum);
            channelEntityRelationInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            channelEntityRelationInfo.setRelationType(ChannelConstants.RELATION_TYPE_SERVICE_NUMBERS_CONTACTS);
            List<ChannelEntityRelationInfo> channelEntityRelationInfoList = channelEntityRelationInfoDao.query(channelEntityRelationInfo);
            if (channelEntityRelationInfoList.size() < 1) {
                map.put("hint", " 根据手机号查询渠道编号:没有渠道实体编号对应该手机号");
                return map;
            }
            Long[] channelEntityIds = new Long[channelEntityRelationInfoList.size()];
            for (int i = 0; i < channelEntityRelationInfoList.size(); i++) {
                channelEntityIds[i] = channelEntityRelationInfoList.get(i).getChannelEntityId();
            }

            SimpleDateFormat sp = new SimpleDateFormat("yyyy/MM/dd hh:mm:ss");

            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityIds(channelEntityIds);
            channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (channelEntityBasicInfoList.size() == 0) {
                map.put("hint", " 根据手机号查询渠道编号:没有渠道实体编号对应该手机号");
                return map;
            } else if (channelEntityBasicInfoList.size() > 1) {
                //				map.put("hint", " 根据手机号查询渠道编号:存在多条记录，主键冲突");
                //				return map;
                throw new Exception(e1);
            }

            channelEntityBasicInfo = channelEntityBasicInfoList.get(0);
            map.put("m_llMarker", "110527");
            map.put("m_nRecStatus", channelEntityBasicInfo.getRecStatus());
            map.put("m_iChannelEntityId", channelEntityBasicInfo.getChannelEntityId());
            map.put("m_dateDoneDate", channelEntityBasicInfo.getDoneDate() == null ? "" : sp.format(channelEntityBasicInfo.getDoneDate()));
            map.put("m_strChannelEntitySerial", channelEntityBasicInfo.getChannelEntitySerial() == null ? "" : channelEntityBasicInfo.getChannelEntitySerial());
            map.put("m_iDistrictId", channelEntityBasicInfo.getDistrictId() == null ? 0 : channelEntityBasicInfo.getDistrictId());
            map.put("m_nChannelEntityType", "");//老接口：1 代理商（非单点商） 2网点（非单点商的下属网点）3 代理商：单点商  4 网点 单点商下属网点
            map.put("m_llDoneCode", channelEntityBasicInfo.getDoneCode());
            map.put("m_strChannelEntityName", channelEntityBasicInfo.getChannelEntityName());
            map.put("m_iOrgId", channelEntityBasicInfo.getOrgId());
            map.put("m_iOpId", channelEntityBasicInfo.getOpId());
            map.put("m_nIsNetwork", 0);//是否联网
            map.put("m_nIsExclusive", 0);//是否排他
            map.put("m_strRegMobile", "");//注册号码
            map.put("m_nChannelEntityStatus", channelEntityBasicInfo.getChannelEntityStatus() == null ? 0 : channelEntityBasicInfo.getChannelEntityStatus());
            map.put("m_strExt1", channelEntityBasicInfo.getExt1() == null ? "" : channelEntityBasicInfo.getExt1());
            map.put("m_iExt2", channelEntityBasicInfo.getExt2() == null ? "" : channelEntityBasicInfo.getExt2());
            map.put("m_strExt3", channelEntityBasicInfo.getExt3() == null ? "" : channelEntityBasicInfo.getExt3());
            if (channelEntityBasicInfo.getChannelEntityType() == ChannelConstants.CHANNEL_ENTITY_TYPE_NODE) {
                ChannelNode channelNode = new ChannelNode();
                channelNode.setNodeId(channelEntityBasicInfo.getChannelEntityId());
                channelNode.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                List<ChannelNode> channelNodeList = channelNodeDao.query(channelNode);
                if (channelNodeList.size() != 0) {
                    channelNode = channelNodeList.get(0);
                    map.put("m_nIsNetwork", channelNode.getIsNetwork() == null ? 0 : channelNode.getIsNetwork());//是否联网
                    map.put("m_nIsExclusive", channelNode.getIsExclusive() == null ? 0 : channelNode.getIsExclusive());//是否排他
                }
                ChannelNodeExtinfo channelNodeExtinfo = new ChannelNodeExtinfo();
                channelNodeExtinfo.setNodeId(channelEntityBasicInfo.getChannelEntityId());
                channelNodeExtinfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                List<ChannelNodeExtinfo> channelNodeExtinfoList = channelNodeExtinfoDao.query(channelNodeExtinfo);
                if (channelNodeExtinfoList.size() != 0) {
                    channelNodeExtinfo = channelNodeExtinfoList.get(0);
                    map.put("m_strRegMobile", channelNodeExtinfo.getRegMobile() == null ? "" : channelNodeExtinfo.getRegMobile());//注册号码
                }
                ChannelIsSingle channelIsSingle = new ChannelIsSingle();
                channelIsSingle.setChannelEntityId(channelEntityBasicInfo.getChannelEntityId());
                List<ChannelIsSingle> channelIsSingleList = channelIsSingleDao.query(channelIsSingle);
                if (channelIsSingleList.size() != 0) {
                    map.put("m_nChannelEntityType", channelIsSingleList.get(0).getChannelEntityType());
                } else {
                    map.put("m_nChannelEntityType", 2);
                }
            }

            ChannelIsSingle channelIsSingle = new ChannelIsSingle();
            channelIsSingle.setChannelEntityId(channelEntityBasicInfo.getChannelEntityId());
            List<ChannelIsSingle> channelIsSingleList = channelIsSingleDao.query(channelIsSingle);
            if (channelIsSingleList.size() != 0) {
                map.put("m_nChannelEntityType", channelIsSingleList.get(0).getChannelEntityType());
            } else {
                map.put("m_nChannelEntityType", 1);
            }

        } catch (Exception e) {
            if (e.getMessage().equals(e1)) {
                throw e;
            }
            throw new Exception(" 根据手机号查询渠道编号 数据库连接异常");
        }
        logger.debug("********    get_channelInfoByMobileNum  end  ********");
        return map;
    }

    /**
     * @param : strBillId 					订单联系人号码
     * @Desc : 根据订单号码查询额度——配合短营改造
     * @return: strRespText                    配额信息
     */
    public Map<String, Object> get_agentLimit(String param) throws Exception {
        logger.debug("********    get_agentLimit  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String svrNum;
        Long agentId;
        Long nodeId;
        Long czCardLimit = 0l;
        Long yhCardLimit = 0L;
        Long czCardOrdered = 0L;
        Long yhCardOrdered = 0L;
        String strRespText = "";
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            svrNum = jsonParam.getString("strBillId");
        } catch (Exception e) {
            logger.error("查询额度——配合短营改造" + e.getMessage());
            logger.debug("********    get_agentLimit  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        if (11 != svrNum.length()) {
            map.put("hint", "号码不符合要求");
            return map;
        }

        //查询准入状态的号码对应的门店
        try {
            //根据订单号码查询代理商
            ChannelEntityRelationInfo channelEntityRelationInfo = new ChannelEntityRelationInfo();
            channelEntityRelationInfo.setRelationType(ChannelConstants.RELATION_TYPE_ORDER_CONTACTS);
            channelEntityRelationInfo.setRelationMobile(svrNum);
            channelEntityRelationInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelEntityRelationInfo> channelEntityRelationInfoList = channelEntityRelationInfoDao.query(channelEntityRelationInfo);

            if (channelEntityRelationInfoList.size() == 0) {
                map.put("hint", "没有查询到准入状态的代理商信息");
                return map;
            }
            Long[] channelEntityIds = new Long[channelEntityRelationInfoList.size()];
            for (int i = 0; i < channelEntityRelationInfoList.size(); i++) {
                channelEntityIds[i] = channelEntityRelationInfoList.get(i).getChannelEntityId();
            }
            //验证代理商状态是否有效
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityIds(channelEntityIds);
            channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            channelEntityBasicInfo.setChannelEntityType(ChannelConstants.CHANNEL_ENTITY_TYPE_AGENT);
            channelEntityBasicInfo.setChannelEntityStatus(ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (channelEntityBasicInfoList.size() == 0) {
                map.put("hint", "没有查询到准入状态的代理商信息");
                return map;
            }
            //查询代理商下属网点
            agentId = channelEntityBasicInfoList.get(0).getChannelEntityId();
            ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
            channelEntityRelInfo.setParentEntity(agentId);
            channelEntityRelInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.query(channelEntityRelInfo);
            if (channelEntityRelInfoList.size() == 0) {
                map.put("hint", "没有查询到准入状态的网点信息");
                return map;
            }
            //验证网点状态是否有效
            nodeId = channelEntityRelInfoList.get(0).getChannelEntityId();
            channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(nodeId);
            channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            Integer[] channelEntityStatuss = {ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT, ChannelConstants.CHANNEL_ENTITY_STATUS_NORMAL_OPERATION,
                    ChannelConstants.CHANNEL_ENTITY_STATUS_CLOSE_UP_SHOP, ChannelConstants.CHANNEL_ENTITY_STATUS_PAUSE_BUSINESS};
            channelEntityBasicInfo.setChannelEntityStatuss(channelEntityStatuss);
            channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (channelEntityBasicInfoList.size() == 0) {
                map.put("hint", "没有查询到准入状态的网点信息");
                return map;
            }
        } catch (Exception e) {
            logger.error("查询额度——配合短营改造" + e.getMessage());
            logger.debug("********    get_agentLimit  end  ********");
            throw new Exception("查询代理商信息发生错误");
        }

        //取限额
        try {
            ChannelNode channelNode = new ChannelNode();
            channelNode.setNodeId(nodeId);
            channelNode.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelNode> channelNodeList = channelNodeDao.query(channelNode);
            if (channelNodeList.size() > 0) {
                channelNode = channelNodeList.get(0);
                czCardLimit = channelNode.getCzcardLimit() == null ? 0 : channelNode.getCzcardLimit();
                yhCardLimit = channelNode.getYhcardLimit() == null ? 0 : channelNode.getYhcardLimit();
            } else if (czCardLimit < 0 || yhCardLimit < 0) {
                channelNode.setRecStatus(ChannelConstants.REC_STATUS_CUR);
                channelNodeList = channelNodeDao.getNodeBeforeExpire(nodeId);
                czCardLimit = channelNode.getCzcardLimit() == null ? 0 : channelNode.getCzcardLimit();
                yhCardLimit = channelNode.getYhcardLimit() == null ? 0 : channelNode.getYhcardLimit();
            }
        } catch (Exception e) {
            logger.error("查询额度——配合短营改造" + e.getMessage());
            logger.debug("********    get_agentLimit  end  ********");
            throw new Exception("取限额发生错误");
        }

        //取本月已下订单的冲值卡的总金额和有号卡的总张数
        try {
            ChannelOrderDtl channelOrderDtl = new ChannelOrderDtl();
            channelOrderDtl.setChannelEntityId(nodeId);
            List<ChannelOrderDtl> channelOrderDtlList = channelOrderDtlDao.getOrderNumberByNodeId(channelOrderDtl);
            if (channelOrderDtlList.size() > 0) {
                for (ChannelOrderDtl cod : channelOrderDtlList) {
                    Integer resType = cod.getResType();
                    if (resType == 1 || resType == 3) {
                        yhCardOrdered += cod.getOrderResNum();
                    } else if (resType == 2) {
                        czCardOrdered += cod.getUnitPrice();
                    }
                }
            }

            channelOrderDtl.setChannelEntityId(agentId);
            channelOrderDtlList = channelOrderDtlDao.getOrderNumberByAgentId(channelOrderDtl);
            if (channelOrderDtlList.size() > 0) {
                for (ChannelOrderDtl cod : channelOrderDtlList) {
                    Integer resType = cod.getResType();
                    if (resType == 1 || resType == 3) {
                        yhCardOrdered += cod.getOrderResNum();
                    } else if (resType == 2) {
                        czCardOrdered += cod.getUnitPrice();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("查询额度——配合短营改造" + e.getMessage());
            logger.debug("********    get_agentLimit  end  ********");
            throw new Exception("取本月已下订单的冲值卡的总金额和有号卡的总张数发生错误");
        }

        if (czCardLimit < czCardOrdered || yhCardLimit < yhCardOrdered) {
            strRespText = "本月您已申领号码卡" + yhCardOrdered + "张，充值卡" + czCardOrdered + "元，已经超过限额！";
            map.put("hint", strRespText);
            return map;
        }

        strRespText = "本月您已申领号码卡" + yhCardOrdered + "张，充值卡" + czCardOrdered + "元。还剩余可申领的号码卡" +
                (yhCardLimit - yhCardOrdered) + "张，充值卡" + (czCardLimit - czCardOrdered) + "元！";
        map.put("strRespText", strRespText);
        return map;
    }

    /**
     * @param : nType
     * @param : iOrgId 						所属组织
     * @param : strRegBillId 				空选号码
     * @Desc : 根据OrgId或手机号（空选母卡）获取代理商ID和网点ID
     * @return: iNodeId                        网点编号
     * @return: iAgentId                    代理商编号
     */
    public Map<String, Object> get_entityIdByOrgIdOrMobile(String param) throws Exception {
        logger.debug("********    get_entityIdByOrgIdOrMobile  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("iNodeId", 0);
        map.put("iAgentId", 0);
        int nType;
        Long orgId;
        String strRegBillId;
        Long nodeId = 0l;
        Long agentId = 0l;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            nType = jsonParam.getInt("nType");
            orgId = jsonParam.getLong("iOrgId");
            strRegBillId = jsonParam.getString("strRegBillId");
        } catch (Exception e) {
            logger.error("根据OrgId或手机号获取代理商ID和网点ID失败:" + e.getMessage());
            logger.debug("********    get_entityIdByOrgIdOrMobile  end  ********");
            //			throw new Exception("根据OrgId或手机号获取代理商ID和网点ID失败");
            return map;
        }

        try {
            if (0 == nType) {
                //根据组织关系获得网点编号
                ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
                channelOrgAgent.setOrgId(orgId);
                List<ChannelOrgAgent> channelOrgAgentList = channelOrgAgentDao.query(channelOrgAgent);
                if (channelOrgAgentList.size() == 0) {
                    return map;
                }
                nodeId = channelOrgAgentList.get(0).getAgentId();
                ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
                channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                channelEntityBasicInfo.setChannelEntityId(nodeId);
                List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                        channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                if (channelEntityBasicInfoList.size() == 0) {
                    return map;
                }
            } else if (1 == nType) {
                //根据空选号码获得网点编号
                AgentChoosephoneAir agentChoosephoneAir = new AgentChoosephoneAir();
                agentChoosephoneAir.setAgentStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                agentChoosephoneAir.setRegBillId(strRegBillId);
                List<AgentChoosephoneAir> agentChoosephoneAirList = agentChoosephoneAirDao.query(agentChoosephoneAir);
                if (agentChoosephoneAirList.size() == 0) {
                    return map;
                }
                nodeId = Long.parseLong(agentChoosephoneAirList.get(agentChoosephoneAirList.size() - 1).getExt1());
                ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
                channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                channelEntityBasicInfo.setChannelEntityId(nodeId);
                List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                        channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                if (channelEntityBasicInfoList.size() == 0) {
                    return map;
                }
            }
        } catch (Exception e) {
            logger.error("根据OrgId或手机号获取代理商ID和网点ID失败:" + e.getMessage());
            logger.debug("********    get_entityIdByOrgIdOrMobile  end  ********");
            //			throw new Exception("根据OrgId或手机号获取代理商ID和网点ID失败");
            return map;
        }

        try {
            //查询网点所属的代理商
            map.put("iNodeId", nodeId);
            ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
            channelEntityRelInfo.setChannelEntityId(nodeId);
            channelEntityRelInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.query(channelEntityRelInfo);
            if (channelEntityRelInfoList.size() == 0) {
                return map;
            }
            agentId = channelEntityRelInfoList.get(0).getParentEntity();
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            channelEntityBasicInfo.setChannelEntityId(agentId);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                    channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (channelEntityBasicInfoList.size() == 0) {
                return map;
            }
            map.put("iAgentId", agentId);
        } catch (Exception e) {
            logger.error("根据OrgId或手机号获取代理商ID和网点ID失败:" + e.getMessage());
            logger.debug("********    get_entityIdByOrgIdOrMobile  end  ********");
            //			throw new Exception("根据OrgId或手机号获取代理商ID和网点ID失败");
            return map;
        }
        return map;
    }

    /**
     * @param : cPrivData 					操作员编号,组织单元信息
     * @param : lChannelId 					渠道实体编号（入参）
     * @param : lBillMonth 					查询的月份（入参）
     * @Desc : 根通过渠道实体ID 和月份获取一个渠道实体当月的的各项酬金
     * @return: CChannelAwardInfo            查询结果，酬金汇总列表
     */
    @Override
    public Map<String, Object> select_ChannelEntityAward(String param) throws Exception {
        logger.debug("********    select_ChannelEntityAward  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("m_llMarker", 31);
        map.put("m_llServerfeetotal", 0);
        map.put("m_llExtint", 0);
        map.put("m_llBusifeetotal", 0);
        map.put("m_llUsefeetotal", 0);
        map.put("m_llReducefeetotal", 0);
        map.put("m_strExtstr", 0);
        map.put("m_llBackfeetotal", 0);
        //渠道编号
        Long lChannelId;
        //查询月份
        Integer iBillMonth;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            lChannelId = jsonParam.getLong("lChannelId");
            iBillMonth = jsonParam.getInt("lBillMonth");
        } catch (Exception e) {
            logger.error("select_ChannelEntityAward" + e.getMessage());
            logger.debug("********    select_ChannelEntityAward  end  ********");
            return map;
        }
        Long currentDate = Long.parseLong(DateUtil.formatCurrentDate("yyyyMM"));
        if (iBillMonth < 200001 || iBillMonth > currentDate) {
            throw new Exception("查询的月份超过范围!");
        }
        //网点
        String strNodeList = "";
        String e1 = "查询渠道实体类型不是符合要求的类型，需要是网点或者代理商的编号。!";
        try {
            //获取渠道类型
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(lChannelId);
            channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (1 != channelEntityBasicInfoList.size()) {
                throw new Exception(e1);
            }

            int channelEntityType = channelEntityBasicInfoList.get(0).getChannelEntityType();

            //为代理商则取代理商写所有网点
            if (ChannelConstants.CHANNEL_ENTITY_TYPE_AGENT == channelEntityType) {
                strNodeList = " in ( select distinct r.channel_entity_id  from channel_entity_rel_info r" +
                        "  where  r.rec_status = 1 and r.parent_entity = " + lChannelId + ")";
            } else if (ChannelConstants.CHANNEL_ENTITY_TYPE_NODE == channelEntityType) {
                //为网点
                strNodeList = " = " + lChannelId;
            }
        } catch (Exception e) {
            logger.error("获取渠道实体类型失败!" + e.getMessage());
            if (e.getMessage().equals(e1)) {
                throw e;
            }
            return map;
        }
        try {
            //业务酬金
            long busiFee = agentBusiAmountDao.getFeeByMonthOrNodeList(strNodeList, iBillMonth);
            long busiFeeExt = agentBusiExtamountDao.getFeeByMonthOrNodeList(strNodeList, iBillMonth);
            //查询服务酬金
            long serviceFee = agentServiceFeeDao.getServiceFeeByMonthOrNodeList(strNodeList, iBillMonth);
            //查询回执酬金
            long reFee = agentBusiAmountDao.getReFeeByMonthOrNodeList(strNodeList, iBillMonth);
            long reFeeExt = agentBusiExtamountDao.getReFeeByMonthOrNodeList(strNodeList, iBillMonth);
            //调整酬金即 场地使用费
            long areaFee = nodeFeeDtlDao.getAreaFeeByMonthOrNodeList(strNodeList, iBillMonth);
            //抵扣场地使用费  合计
            long reduceFee = chnlNodeFeeInfoDao.getReduceFeeByMonthOrNodeList(strNodeList, iBillMonth);


            map.put("m_llBusifeetotal", busiFee + busiFeeExt);
            map.put("m_llServerfeetotal", serviceFee);
            map.put("m_llUsefeetotal", areaFee);
            map.put("m_llReducefeetotal", reduceFee);
            map.put("m_llBackfeetotal", reFee + reFeeExt);
        } catch (Exception e) {
            logger.error("---------select_ChannelEntityAward----------" + e.getMessage());
            return map;
        }

        try {
            //关于“代理查询”记录登录信息的需求
            AgentQueryRecord agentQueryRecord = new AgentQueryRecord();
            agentQueryRecord.setChannelEntityId(lChannelId);
            agentQueryRecord.setBusiType(11);
            agentQueryRecord.setBusiName("网站/渠道代理商服务酬金查询");
            agentQueryRecord.setDoneDate(DateUtil.getCurrDate());
            agentQueryRecord.setFunctionName("select_ChannelEntityAward");
        } catch (Exception e) {
            logger.error("=========select_ChannelEntityAward=========" + e.getMessage());
        }

        return map;
    }

    /**
     * @param : nType
     * @param : iOrgId 						所属组织
     * @param : strRegBillId 				空选号码
     * @Desc : 根据OrgId或手机号获取代理商ID，网点ID，所属分公司
     * @return: iNodeId                        网点编号
     * @return: iAgentId                    代理商编号
     */
    @Override
    public Map<String, Object> getEntityInfoByOrgIdOrMonile(String param) throws Exception {
        logger.debug("********    get_entityIdByOrgIdOrMobile  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("iNodeId", 0);
        map.put("iAgentId", 0);
        map.put("strFiliale", "");
        int nType;
        Long orgId;
        String strRegBillId;
        Long nodeId = 0l;
        Long agentId = 0l;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            nType = jsonParam.getInt("nType");
            orgId = jsonParam.getLong("iOrgId");
            strRegBillId = jsonParam.getString("strRegBillId");
        } catch (Exception e) {
            logger.error("根据OrgId或手机号获取代理商ID和网点ID失败:" + e.getMessage());
            logger.debug("********    get_entityIdByOrgIdOrMobile  end  ********");
            //			throw new Exception("根据OrgId或手机号获取代理商ID和网点ID失败");
            return map;
        }

        try {
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList = new ArrayList<ChannelEntityBasicInfo>();
            if (0 == nType) {
                //根据组织关系获得网点编号
                ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
                channelOrgAgent.setOrgId(orgId);
                List<ChannelOrgAgent> channelOrgAgentList = channelOrgAgentDao.query(channelOrgAgent);
                if (channelOrgAgentList.size() == 0) {
                    return map;
                }
                nodeId = channelOrgAgentList.get(0).getAgentId();
                ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
                channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                channelEntityBasicInfo.setChannelEntityId(nodeId);
                channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                if (channelEntityBasicInfoList.size() == 0) {
                    return map;
                }
            } else if (1 == nType) {
                //根据空选号母卡号码获得网点编号
                AgentChoosephoneAir agentChoosephoneAir = new AgentChoosephoneAir();
                agentChoosephoneAir.setAgentStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                agentChoosephoneAir.setRegBillId(strRegBillId);
                List<AgentChoosephoneAir> agentChoosephoneAirList = agentChoosephoneAirDao.query(agentChoosephoneAir);
                if (agentChoosephoneAirList.size() == 0) {
                    return map;
                }
                nodeId = Long.parseLong(agentChoosephoneAirList.get(agentChoosephoneAirList.size() - 1).getExt1());
                ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
                channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                channelEntityBasicInfo.setChannelEntityId(nodeId);
                channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                if (channelEntityBasicInfoList.size() == 0) {
                    return map;
                }
            } else if (2 == nType) {
                nodeId = orgId;
                ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
                channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                channelEntityBasicInfo.setChannelEntityId(nodeId);
                channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                if (channelEntityBasicInfoList.size() == 0) {
                    return map;
                }
            }
            //获取网点行政区
            Integer regionId = channelEntityBasicInfoList.get(0).getRegionId();
            List<ChannelSysBaseType> ChannelSysBaseInfoList =
                    ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.CHANNEL_SYS_BASE_TYPE_CODETYPE_REGION_ID, regionId, null, null);
            String filiale = ChannelSysBaseInfoList.get(0).getExt1();

            //获取对应分公司名称
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(Long.parseLong(filiale));
            channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            map.put("strFiliale", channelEntityBasicInfoList.get(0).getChannelEntityName());
        } catch (Exception e) {
            logger.error("根据OrgId或手机号获取代理商ID和网点ID失败:" + e.getMessage());
            logger.debug("********    get_entityIdByOrgIdOrMobile  end  ********");
            //			throw new Exception("根据OrgId或手机号获取代理商ID和网点ID失败");
            return map;
        }

        try {
            //查询网点所属的代理商
            map.put("iNodeId", nodeId);
            ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
            channelEntityRelInfo.setChannelEntityId(nodeId);
            channelEntityRelInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.query(channelEntityRelInfo);
            if (channelEntityRelInfoList.size() == 0) {
                return map;
            }
            agentId = channelEntityRelInfoList.get(0).getParentEntity();
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            channelEntityBasicInfo.setChannelEntityId(agentId);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                    channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (channelEntityBasicInfoList.size() == 0) {
                return map;
            }
            map.put("iAgentId", agentId);
        } catch (Exception e) {
            logger.error("根据OrgId或手机号获取代理商ID和网点ID失败:" + e.getMessage());
            logger.debug("********    get_entityIdByOrgIdOrMobile  end  ********");
            //			throw new Exception("根据OrgId或手机号获取代理商ID和网点ID失败");
            return map;
        }
        return map;
    }

    /**
     * @param : month 					1-上个月，2-上两个月，3-上三个月
     * @param : billId 					代理商服务号码
     * @Desc : 根据代理商服务号码获取代理商酬金信息
     * @return: iNodeId                        网点编号
     * @return: iAgentId                    代理商编号
     */
    @Transactional
    public Map<String, Object> getAgentAwardByBillId(String param) throws Exception {
        logger.info("********    getAgentAwardByBillId  begin  ********");
        Map<String, Object> reMap = new HashMap<String, Object>();
        Map<String, Object> map = new HashMap<String, Object>();
        int return_success = 0;
        int return_exception = 1;
        String billId;
        int month = 1;
        Long agentId;

        map.put("cardFee", "");
        map.put("czFee", "");
        map.put("terminalFee", "");
        map.put("businessAcceptFee", "");
        map.put("fine", "");
        map.put("storeFee", "");
        map.put("increaseFee", "");
        map.put("totalFee", "");
        map.put("channelEntityName", "");
        map.put("channelEntityId", "");
        reMap.put("status", return_exception);
        reMap.put("message", "success");
        reMap.put("data", map);
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            month = jsonParam.getInt("month");
            billId = jsonParam.getString("billId");
        } catch (Exception e) {
            logger.error("根据代理商服务号码获取代理商酬金信息失败:" + e.getMessage());
            logger.debug("********    getAgentAwardByBillId  end  ********");
            //			throw new Exception("传参不合格");
            reMap.put("message", e.getMessage());
            return reMap;
        }
        String e1 = "没有手机号码对应的代理商";
        //根据代理商服务号码获取代理商ID
        try {
            ChannelEntityRelationInfo channelEntityRelationInfo = new ChannelEntityRelationInfo();
            channelEntityRelationInfo.setRelationType(ChannelConstants.RELATION_TYPE_SERVICE_NUMBERS_CONTACTS);
            channelEntityRelationInfo.setRelationMobile(billId);
            channelEntityRelationInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityRelationInfoDao.getAgentIdByMobile(channelEntityRelationInfo);

            if (channelEntityBasicInfoList.size() == 0) {
                throw new Exception("没有手机号码对应的代理商");
            }
            agentId = channelEntityBasicInfoList.get(0).getChannelEntityId();
        } catch (Exception e) {
            logger.error("根据代理商服务号码获取代理商酬金信息失败:" + e.getMessage());
            logger.debug("********    getAgentAwardByBillId  end  ********");
            //			reMap.put("message", e.getMessage());
            //			return reMap;
            throw e;
        }

        String e2 = "酬金数据暂无";
        String e3 = "无法查询三个月以上的酬金数据";
        String e4 = "无该代理商酬金数据";
        try {
            if (1 == month) {
                //每月前5天，不提供上个月酬金数据查询查询
                if (5 >= Integer.parseInt(DateUtil.formatCurrentDate("dd"))) {
                    throw new Exception(e2);
                }
            }

            //确认month只提供最多前三个月的查询
            if (month < 1 || month > 3) {
                throw new Exception(e3);
            }

            //根据入参month设置查哪张月表
            String partion;
            Calendar cel = Calendar.getInstance();
            SimpleDateFormat sp = new SimpleDateFormat("yyyyMM");
            cel.add(Calendar.MONTH, -month);
            partion = sp.format(cel.getTime());

            RptDPayDirect rptDPayDirect = new RptDPayDirect();
            rptDPayDirect.setChannelEntityId(agentId.toString());
            rptDPayDirect.setPartion(partion);
            //获取酬金相关数据
            List<RptDPayDirect> rptDPayDirectList = rptDPayDirectDao.query(rptDPayDirect);
            if (0 == rptDPayDirectList.size()) {
                throw new Exception(e4);
            }
            rptDPayDirect = rptDPayDirectList.get(0);
            //用户入网手续费(卡号)
            String cardFee = rptDPayDirect.getCardFee();
            //用户入网调整费(卡号)
            String cardAdjustFee = rptDPayDirect.getCardAdjustFee();
            //充值卡手续费
            String qzcardFee = rptDPayDirect.getQzcardFee();
            //充值卡调整费
            String qzcardAdjustFee = rptDPayDirect.getQzcardAdjustFee();
            //增值业务服务费
            String increaseFee = rptDPayDirect.getIncreaseFee();
            //增值业务调整费
            String increaseAdjustFee = rptDPayDirect.getIncreaseAdjustFee();
            //终端服务费
            String terminalFee = rptDPayDirect.getTerminalFee();
            //终端调整费
            String terminalAdjustFee = rptDPayDirect.getTerminalAdjustFee();
            //门店补贴
            String storeSubsidy = rptDPayDirect.getStoreSubsidy();
            //门店补贴调整
            String storeAdjustSubsidy = rptDPayDirect.getStoreAdjustSubsidy();
            //业务受理酬金
            String businessAccept = rptDPayDirect.getBusinessAccept();
            //违规扣款(已经是负数)
            String fine = rptDPayDirect.getFine();
            //代理商简称
            String channelEntityName = rptDPayDirect.getChannelEntityName();
            //网点
            String nodeName = rptDPayDirect.getNodeName();
            //姓名
            String userName = rptDPayDirect.getUserName();
            //身份证号
            String idCard = rptDPayDirect.getIdCard();

            //代理商标识
            String channelEntityId = rptDPayDirect.getChannelEntityId();

            //卡号销售酬金
            Long khxscj = Long.parseLong(cardFee) + Long.parseLong(cardAdjustFee);
            //充值酬金
            Long czcj = Long.parseLong(qzcardFee) + Long.parseLong(qzcardAdjustFee);
            //终端销售酬金
            Long zdxscj = Long.parseLong(terminalFee) + Long.parseLong(terminalAdjustFee);
            //业务受理酬金
            Long ywslcj = Long.parseLong(businessAccept);
            //违规扣款
            Long wgkk = Long.parseLong(fine);
            //门店奖励
            Long mdjl = Long.parseLong(storeSubsidy) + Long.parseLong(storeAdjustSubsidy);
            //增值业务酬金(暂时不算在酬金总计里)
            Long zzywcj = Long.parseLong(increaseFee) + Long.parseLong(increaseAdjustFee);

            //酬金总计
            Long totalFee = khxscj + czcj + zdxscj + ywslcj + wgkk + mdjl;
            map.put("cardFee", khxscj);
            map.put("czFee", czcj);
            map.put("terminalFee", zdxscj);
            map.put("businessAcceptFee", ywslcj);
            map.put("fine", wgkk);
            map.put("storeFee", mdjl);
            map.put("increaseFee", zzywcj);
            map.put("totalFee", totalFee);
            map.put("channelEntityName", channelEntityName);
            map.put("channelEntityId", channelEntityId);
            reMap.put("status", return_success);
            reMap.put("message", "success");
            reMap.put("data", map);
        } catch (Exception e) {
            logger.error("根据代理商服务号码获取代理商酬金信息失败:" + e.getMessage());
            logger.debug("********    getAgentAwardByBillId  end  ********");
            reMap.put("message", e.getMessage());
            return reMap;
        }
        logger.debug("********    getAgentAwardByBillId  end  ********");
        return reMap;
    }

    /**
     * 通过父组织查询下一级渠道组织树信息,该接口只供业务办理使用
     *
     * @param param
     * @return
     */
    public List<JSONObject> get_channelParentTreeView(String param) {
        Long channelEntityId = null;
        Integer channelEntityType;
        Integer channelEntitySecondType = null;
        Integer sortType = null;
        String seqEntityStatus = null;
        List<JSONObject> list = new ArrayList<JSONObject>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            channelEntityId = jsonParam.getLong("iChannelEntityId");
            channelEntityType = jsonParam.getInt("nChannelEntityType");
            channelEntitySecondType = jsonParam.getInt("nChannelEntitySecondType");
            sortType = jsonParam.getInt("nSortType");
            seqEntityStatus = jsonParam.getString("seqEntityStatus");
        } catch (Exception e) {
            logger.error("通过父组织查询下一级渠道组织树信息,该接口只供业务办理使用失败" + e.getMessage());
            logger.debug("********    get_entityIdByOrgIdOrMobile  end  ********");
            //			throw new Exception("根据OrgId或手机号获取代理商ID和网点ID失败");
            //			return map;
        }
        String strCond = "where parent_entity = " + channelEntityId;
        seqEntityStatus = seqEntityStatus.replace("[", "");
        seqEntityStatus = seqEntityStatus.replace("]", "");
        seqEntityStatus = seqEntityStatus.replace(" ", "");
        String[] str1 = seqEntityStatus.split(",");
        int[] listEntityStatus = new int[str1.length];
        if (!"".equals(seqEntityStatus)) {
            for (int i = 0; i < str1.length; i++) {
                listEntityStatus[i] = Integer.valueOf(str1[i]);
            }
        }
        if (channelEntityId == -1) {
            strCond = strCond + " and CHANNEL_ENTITY_TYPE <> -1 and CHANNEL_ENTITY_STATUS <> -1 ";
        } else {
            //strCond = strCond + " and channel_entity_second_type = " + channelEntitySecondType;
            if (listEntityStatus.length == 0) {
                strCond = strCond + "and CHANNEL_ENTITY_STATUS <> -1 ";
            } else if (listEntityStatus.length == 1) {
                if (listEntityStatus[0] == 0) {
                    strCond = strCond + " and CHANNEL_ENTITY_STATUS <> -1 ";
                } else {
                    strCond = strCond + " and CHANNEL_ENTITY_STATUS = " + listEntityStatus[0];
                }
            } else {
                strCond = strCond + " and CHANNEL_ENTITY_STATUS in (" + listEntityStatus[0];
                for (int iNdex = 1; iNdex < listEntityStatus.length; iNdex++) {
                    strCond = strCond + "," + listEntityStatus[iNdex];
                }
                strCond = strCond + ") ";
            }
        }
        if (sortType == 1) {
            strCond = strCond + " order by channel_entity_id ";
        } else if (sortType == 2) {
            strCond = strCond + " order by channel_entity_status,nlssort(channel_entity_name,'NLS_SORT=SCHINESE_PINYIN_M') asc";
        } else if (sortType == 3) {
            // 未定义
        } else {
            strCond = strCond + " order by channel_entity_id";
        }

        try {
            List<ChannelParentTreeView> channelParentTreeViewList = channelEntityBasicInfoDao.getChannelParentTreeView(strCond);
            for (ChannelParentTreeView channelParentTreeView : channelParentTreeViewList) {
                JSONObject reJson = JSONObject.fromObject(channelParentTreeView);
                list.add(reJson);
            }
        } catch (Exception e) {
            logger.error("通过父组织查询下一级渠道组织树信息,该接口只供业务办理使用失败" + e.getMessage());
        }

        return list;
    }

    /**
     * @param :billId 该代理商的服务号码
     * @return
     * @throws Exception
     * @Desc 根据代理商的服务号码查询门店信息
     */
    public Map<String, Object> queryNodeInfo(String param) throws Exception {
        logger.debug("********    queryNodeInfo  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String billId;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            billId = jsonParam.getString("billId");
        } catch (Exception e) {
            logger.error("根据代理商的服务号码查询门店信息失败:" + e.getMessage());
            logger.debug("********    get_entityIdByOrgIdOrMobile  end  ********");
            throw new Exception("根据代理商的服务号码查询门店信息失败:");
        }

        try {
            //根据服务号码查询代理商编号
            ChannelEntityRelationInfo channelEntityRelationInfo = new ChannelEntityRelationInfo();
            channelEntityRelationInfo.setRelationMobile(billId);
            channelEntityRelationInfo.setRelationType(ChannelConstants.RELATION_TYPE_SERVICE_NUMBERS_CONTACTS);
            channelEntityRelationInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);

            List<ChannelEntityRelationInfo> channelEntityRelationInfoList =
                    channelEntityRelationInfoDao.query(channelEntityRelationInfo);
            if (channelEntityRelationInfoList.size() == 0) {
                throw new Exception("没有服务号码对应的代理商");
            }
            Long agentId = channelEntityRelationInfoList.get(0).getChannelEntityId();

            //获取订单联系人信息
            channelEntityRelationInfo = new ChannelEntityRelationInfo();
            channelEntityRelationInfo.setRelationType(ChannelConstants.RELATION_TYPE_ORDER_CONTACTS);
            channelEntityRelationInfo.setChannelEntityId(agentId);
            channelEntityRelationInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            channelEntityRelationInfoList = channelEntityRelationInfoDao.query(channelEntityRelationInfo);
            map.put("agentId", agentId);
            map.put("handleOrg", "");
            map.put("person", channelEntityRelationInfoList.get(0).getRelationName());
            map.put("phone", channelEntityRelationInfoList.get(0).getRelationMobile());

            //获取代理商简称
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(agentId);
            channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                    channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            map.put("agentName", channelEntityBasicInfoList.get(0).getChannelEntityName());
            map.put("company", channelEntityBasicInfoList.get(0).getChannelEntityName());

            //获取代理商支付方式和地址
            ChannelAgentInfo channelAgentInfo = new ChannelAgentInfo();
            channelAgentInfo.setAgentId(agentId);
            channelAgentInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelAgentInfo> channelAgentInfoList = channelAgentInfoDao.query(channelAgentInfo);
            map.put("address", channelAgentInfoList.get(0).getAddress());
            map.put("paymentManner", channelAgentInfoList.get(0).getPaymentManner());

            //获取代理商下属网点
            ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
            channelEntityRelInfo.setParentEntity(agentId);
            channelEntityRelInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.query(channelEntityRelInfo);
            Long nodeId = channelEntityRelInfoList.get(0).getChannelEntityId();

            //根据网点编号获取orgId
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setAgentId(nodeId);
            List<ChannelOrgAgent> channelOrgAgentList = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgentList.size() != 0) {
                map.put("orgId", channelOrgAgentList.get(0).getOrgId());
            } else {
                map.put("orgId", "");
            }

        } catch (Exception e) {
            logger.debug("********    get_entityIdByOrgIdOrMobile  end  ********");
            throw new Exception("根据代理商的服务号码查询门店信息失败：" + e.getMessage());
        }
        logger.debug("********    get_entityIdByOrgIdOrMobile  end  ********");
        return map;
    }

    /**
     * @param :resCode 卡资源类型
     * @return
     * @throws Exception
     * @Desc 查询卡资源类型是否存在
     */
    public Map<String, Object> validateResCode(String param) throws Exception {
        logger.debug("********    validateResCode  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        Long resCode;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            resCode = jsonParam.getLong("resCode");
        } catch (Exception e) {
            logger.error("查询卡资源类型是否存在失败:" + e.getMessage());
            logger.debug("********    validateResCode  end  ********");
            throw new Exception("查询卡资源类型是否存在失败");
        }

        try {
            ChnlResModelDefinition chnlResModelDefinition = new ChnlResModelDefinition();
            chnlResModelDefinition.setResCode(resCode);
            List<ChnlResModelDefinition> chnlResModelDefinitionList = chnlResModelDefinitionDao.query(chnlResModelDefinition);
            if (0 == chnlResModelDefinitionList.size()) {
                map.put("isExist", "false");
            } else {
                map.put("isExist", "true");
            }
        } catch (Exception e) {
            map.put("isExist", "false");
            throw new Exception("查询卡资源类型是否存在失败");
        }
        return map;
    }

    //剔除list中的重复数据
    public List removeDuplicate(List list) {
        HashSet h = new HashSet(list);
        list.clear();
        list.addAll(h);
        return list;
    }

    //获得物流接口相关数据
    public ChannelOrderDelivery getChannelOrderDeliveryExt(ChannelOrderDelivery channelOrderDelivery) throws Exception {
        try {
            //查询代理商全称
            ChannelOrder channelOrder = new ChannelOrder();
//			channelOrder.setChannelOrderId(channelOrderDeliveryList.get(0).getInvoiceNo());
            channelOrder.setChannelOrderId(channelOrderDelivery.getInvoiceNo());
            List<ChannelOrder> channelOrderList = channelOrderDao.query(channelOrder);
            Long agentId = null;
            if (0 < channelOrderList.size()) {
                agentId = channelOrderList.get(0).getChannelEntityId();
                ChannelAgentInfo channelAgentInfo = new ChannelAgentInfo();
                channelAgentInfo.setAgentId(agentId);
                List<ChannelAgentInfo> channelAgentInfoList = channelAgentInfoDao.query(channelAgentInfo);
                if (0 < channelAgentInfoList.size()) {
                    channelOrderDelivery.setEntityname(channelAgentInfoList.get(0).getFullName());
                }
            }
			/*
			for(ChannelOrder co2 :channelOrderList){
				agentId = co2.getChannelEntityId();
				ChannelAgentInfo channelAgentInfo = new ChannelAgentInfo();
				channelAgentInfo.setAgentId(agentId);
				List<ChannelAgentInfo> channelAgentInfoList = channelAgentInfoDao.query(channelAgentInfo);
				if(0 < channelAgentInfoList.size()){
					channelOrderDelivery.setEntityname(co2.getFullName());
				}
			}
			*/
            //查询应付金额
            ChannelOrderPay channelOrderPay = new ChannelOrderPay();
            channelOrderPay.setChannelOrderId(channelOrderDelivery.getInvoiceNo());
            List<ChannelOrderPay> channelOrderPayList = channelOrderPayDao.queryBiggerThanZero(channelOrderPay);
            if (0 < channelOrderPayList.size()) {
                if (channelOrderPayList.get(0).getOrderAmount() != null && channelOrderPayList.get(0).getOrderAmount() > 0) {
                    channelOrderDelivery.setPayableAmount(channelOrderPayList.get(0).getOrderAmount());
                } else {
                    channelOrderDelivery.setPayableAmount(0L);
                }
            }
            //查询银行账号
            ChannelEntityAccRel channelEntityAccRel = new ChannelEntityAccRel();
            if (null != agentId && !agentId.equals("")) {
                channelEntityAccRel.setChannelEntityId(agentId);
                List<ChannelEntityAccRel> channelEntityAccRelList = channelEntityAccRelDao.query(channelEntityAccRel);
                if (0 < channelEntityAccRelList.size()) {
                    Long accId = channelEntityAccRelList.get(0).getAccId();
                    ChannelBankAccount channelBankAccount = new ChannelBankAccount();
                    channelBankAccount.setAccountType(3);
                    channelBankAccount.setAccStatus(1);
                    channelBankAccount.setRecStatus(1);
                    channelBankAccount.setAccId(accId);
                    List<ChannelBankAccount> channelBankAccountList = channelBankAccountDao.query(channelBankAccount);
                    if (0 < channelBankAccountList.size()) {
                        channelOrderDelivery.setCity(channelBankAccountList.get(0).getBankAccount());
                    }
                }
            }

//			channelOrderDelivery.setCity("0");
        } catch (Exception e) {
            logger.error("查询订单物流接口表出错" + e.getMessage());
        }
        return channelOrderDelivery;
    }

    //JSONObject中时间格式转换
    public JSONObject changeTime(JSONObject reJson, String Time) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        String strTime = reJson.getString(Time);
        JSONObject tempTime = JSONObject.fromObject(strTime);
        Date date = new Date();
        date.setTime(tempTime.getLong("time"));
        reJson.put(Time, sdf.format(date));
        return reJson;
    }

    //检验号码合法性
    public void checkBillId(String billId) throws Exception {
        //检查号码是否为空
        if ("" == billId || "".equals(billId)) {
            throw new Exception("手机号不能为空！");
        }
        //检查号码是否满足数字类型
        for (int i = 0; i < billId.length(); i++) {
            if (billId.charAt(i) < '0' || billId.charAt(i) > '9') {
                throw new Exception("手机号码必须是11位数字");
            }
        }
        //检查号码长度是否符合要求
        if (11 != billId.length()) {
            throw new Exception("手机号码必须是11位数字");
        }
    }

    public Double stringToDouble(String str) {
        DecimalFormat df = new DecimalFormat("#####0.00");
        Double strDouble = Double.parseDouble(str);
        return strDouble;
    }

    @Transactional(rollbackFor = Exception.class)
    public void test() throws Exception {
        System.out.println("11111111111");
        ChannelPointAccInfo channelPointAccInfo = new ChannelPointAccInfo();
        channelPointAccInfo.setChannelEntityId(53008766L);
        channelPointAccInfo.setPointAccStatus(4);
        channelPointAccInfoDao.update(channelPointAccInfo);
        System.out.println("2222222222222");


        PointsInfo pointsInfo = new PointsInfo();
        pointsInfo.setPointsSysId(143121111L);
        pointsInfo.setPointsId(14312L);
        pointsInfo.setPointsIdType(4);
        pointsInfoDao.insert(pointsInfo);
        System.out.println("3333333333");
    }

    @Override
    public Map<String, Object> getAgentPhoneLevelSchedule(String param)
            throws Exception {
        logger.debug("号池分级选择 接口调用开始");
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            Long agentId = jsonParam.getLong("agentId");
            logger.info("获取入参的合作方id:" + agentId);
            //调用查询接口
            List<ChannelAgentPhoneLevel> channelAgentPhoneLevels = channelAgentPhoneLevelService.getChannelAgentPhoneLevelAgentId(agentId);
            if (channelAgentPhoneLevels.size() != 1) {
                logger.info("调用查询接口返回数据出现异常:" + channelAgentPhoneLevels.size());
                return map;
            }
            String phoneNumber = "";
            if (channelAgentPhoneLevels.get(0).getPhoneNumber() != null) {
                phoneNumber = String.valueOf(channelAgentPhoneLevels.get(0).getPhoneNumber());
            }
            map.put("agentLevel", channelAgentPhoneLevels.get(0).getAgentLevel());
            map.put("phoneNumber", phoneNumber);
        } catch (Exception e) {
            throw new Exception("号池分级选择 接口调用出现异常");
        }
        return map;
    }

    /***
     * <AUTHOR>
     * 根据空选母卡获取对应的orgId
     * @param      param
     *             空选母卡
     */
    @Override
    public Map<String, Object> getAgentChoosephoneAirBillId(String param) {
        logger.debug("********    getAgentChoosephoneAirBillId  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("iOrgId", 0);
        String regBillId = "";
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            regBillId = jsonParam.getString("regBillId");
        } catch (Exception e) {
            logger.error("根据空选母卡获取对应的orgId失败:" + e.getMessage());
            logger.debug("********    getAgentChoosephoneAirBillId  end  ********");
            return map;
        }
        try {
            AgentChoosephoneAir agentChoosephoneAir = new AgentChoosephoneAir();
            agentChoosephoneAir.setRegBillId(regBillId);
            List<AgentChoosephoneAir> agentChoosephoneAirs = agentChoosephoneAirDao.query(agentChoosephoneAir);
            if (agentChoosephoneAirs.size() <= 0) {
                map.put("iOrgId", "");
            } else {
                ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
                channelOrgAgent.setAgentId(Long.valueOf(agentChoosephoneAirs.get(0).getExt1()));
                List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
                if (channelOrgAgents.size() == 1) {
                    map.put("iOrgId", channelOrgAgents.get(0).getOrgId());
                } else {
                    map.put("iOrgId", "");
                }
            }
        } catch (Exception e) {
            logger.error("根据空选母卡获取对应的orgId失败:" + e.getMessage());
            logger.debug("********    getAgentChoosephoneAirBillId  end  ********");
            return map;
        }
        return map;
    }

    /***
     * 社会渠道授权网点二维码信息查询需求
     * 二维码识别，根据全网统一编码查询对应接口
     * 接口改造，将接口入参改成orgId,根据orgId获取对应网点数据
     */
    @Override
    public Map<String, Object> getChannelNodeUnifyCode(String param) {
        logger.info("二维码识别，根据orgId查询对应接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        String orgId = "";
        String strUnifyCode = "";
        ChannelOrgAgent channelOrgAgent2 = null;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            strUnifyCode = jsonParam.getString("strUnifyCode");
            orgId = jsonParam.getString("orgId");
        } catch (Exception e) {
            logger.error("获取入参参数：二维码识别，根据orgId查询对应接口失败:", e);
            logger.debug("********    二维码识别，根据orgId查询对应接口  结束  ********");
            map.put("error", "入参格式错误");
            return map;
        }
        try {
            /****
             * 逻辑：
             * 1：根据orgId查询实体对应关系中，orgId作为条件查询，如果无值，在根据nodeId作为条件查询，如果还是无值，则直接返回，无网点信息。
             * 2：如果根据orgId查询到值，则使用对应查询到的nodeId
             * 3：如果orgId无查询到值，而nodeId查询到值，则使用nodeId
             * 4：根据nodeId查询channel_node表获取网点是否存在，然后查询channel_entity_base_info表查询网点是否有效，然后返回
             */
            //1
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setOrgId(Long.valueOf(orgId));
            List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgents.size() <= 0) {
                //3
                channelOrgAgent = new ChannelOrgAgent();
                channelOrgAgent.setAgentId(Long.valueOf(orgId));
                List<ChannelOrgAgent> channelOrgAgents1 = channelOrgAgentDao.query(channelOrgAgent);
                if (channelOrgAgents1.size() <= 0) {
                    map.put("strNodeName", "网点信息不存在");
                    map.put("strNodeAddr", "");
                    map.put("strUnifyCode", "");
                    map.put("strBusinessRange", "");
                    return map;
                } else if (channelOrgAgents1.size() == 1) {
                    channelOrgAgent2 = channelOrgAgents1.get(0);
                } else if (channelOrgAgents1.size() > 1) {
                    map.put("strNodeName", "根据nodeId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
                    map.put("strNodeAddr", "");
                    map.put("strUnifyCode", "");
                    map.put("strBusinessRange", "");
                    return map;
                }
            } else if (channelOrgAgents.size() > 1) {
                map.put("strNodeName", "根据orgId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
                map.put("strNodeAddr", "");
                map.put("strUnifyCode", "");
                map.put("strBusinessRange", "");
                return map;
            } else if (channelOrgAgents.size() == 1) {
                channelOrgAgent2 = channelOrgAgents.get(0);
            }
            //公共部分
            ChannelNode channelNode = new ChannelNode();
            channelNode.setNodeId(channelOrgAgent2.getAgentId());
            channelNode.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            List<ChannelNode> channelNodes = channelNodeDao.query(channelNode);
            if (channelNodes.size() <= 0) {
                map.put("strNodeName", "网点信息不存在");
                map.put("strNodeAddr", "");
                map.put("strUnifyCode", "");
                map.put("strBusinessRange", "");
            } else {
                if (channelNodes.size() == 1) {
                    /**
                     * 根据网点id获取对应的网点名称
                     */
                    ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
                    channelEntityBasicInfo.setChannelEntityId(channelNodes.get(0).getNodeId());
                    channelEntityBasicInfo.setChannelEntityType(2);
                    List<ChannelEntityBasicInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                    if (channelEntityBasicInfos.size() <= 0) {
                        map.put("strNodeName", "网点名称信息不存在");
                        map.put("strNodeAddr", "");
                        map.put("strUnifyCode", "");
                        map.put("strBusinessRange", "");
                    } else if (channelEntityBasicInfos.size() == 1) {
                        map.put("strNodeName", channelEntityBasicInfos.get(0).getChannelEntityName());
                        map.put("strNodeAddr", channelNodes.get(0).getNodeAddr());
                        map.put("strUnifyCode", strUnifyCode);
                        map.put("strBusinessRange", "入网业务、充值卡销售、终端业务、增值业务、数据业务办理、实名制登记服务");
                    }
                } else {
                    logger.info("根据网点数据查询出来的网点信息有误:" + channelNodes.size());
                    map.put("strNodeName", "网点信息出现重复");
                    map.put("strNodeAddr", "");
                    map.put("strUnifyCode", "");
                    map.put("strBusinessRange", "");
                }
            }
        } catch (Exception e) {
            logger.error("执行调用接口：二维码识别，根据全网统一编码查询对应接口失败:", e);
            map.put("error", "入参参数类型错误");
            return map;
        }
        logger.info("二维码识别，根据全网统一编码查询对应接口 结束");
        return map;


//        logger.info("二维码识别，根据全网统一编码查询对应接口 开始");
//        Map<String, Object> map = new HashMap<String, Object>();
//        String strUnifyCode = "";
//        try{
//            JSONObject jsonParam = JSONObject.fromObject(param);
//            strUnifyCode = jsonParam.getString("strUnifyCode");
//        }catch(Exception e){
//            logger.error("获取入参参数：二维码识别，根据全网统一编码查询对应接口失败:", e);
//            logger.debug("********    二维码识别，根据全网统一编码查询对应接口  结束  ********");
//            map.put("error", "入参格式错误");
//            return map;
//        }
//        try {
//            ChannelNode channelNode = new ChannelNode();
//            channelNode.setUnifyCode(strUnifyCode);
//            channelNode.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
//            List<ChannelNode> channelNodes = channelNodeDao.query(channelNode);
//            if(channelNodes.size() <= 0){
//                map.put("strNodeName", "网点信息不存在");
//                map.put("strNodeAddr", "");
//                map.put("strUnifyCode", "");
//                map.put("strBusinessRange", "");
//            }else{
//                if(channelNodes.size() == 1){
//                    /**
//                     * 根据网点id获取对应的网点名称
//                     */
//                    ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
//                    channelEntityBasicInfo.setChannelEntityId(channelNodes.get(0).getNodeId());
//                    channelEntityBasicInfo.setChannelEntityType(2);
//                    List<ChannelEntityBasicInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
//                    if(channelEntityBasicInfos.size() <= 0){
//                        map.put("strNodeName", "网点名称信息不存在");
//                        map.put("strNodeAddr", "");
//                        map.put("strUnifyCode", "");
//                        map.put("strBusinessRange", "");
//                    }else if(channelEntityBasicInfos.size() ==1){
//                        map.put("strNodeName", channelEntityBasicInfos.get(0).getChannelEntityName());
//                        map.put("strNodeAddr", channelNodes.get(0).getNodeAddr());
//                        map.put("strUnifyCode", channelNodes.get(0).getUnifyCode());
//                        map.put("strBusinessRange", "入网业务、充值卡销售、终端业务、增值业务、数据业务办理、实名制登记服务");
//                    }
//                }else{
//                    logger.info("根据网点数据查询出来的网点信息有误:"+channelNodes.size());
//                    map.put("strNodeName", "网点信息出现异常");
//                    map.put("strNodeAddr", "");
//                    map.put("strUnifyCode", "");
//                    map.put("strBusinessRange", "");
//                }
//            }
//        } catch (Exception e) {
//            logger.error("执行调用接口：二维码识别，根据全网统一编码查询对应接口失败:", e);
//        }
//        logger.info("二维码识别，根据全网统一编码查询对应接口 结束");
//        return map;
    }

    /**
     * 根据实体ID,查询其相关代理商和所有门店
     *
     * @param param
     * @return
     */
    @Override
    public List<JSONObject> select_channelEntityBasicInfo(String param) throws Exception {

        Map<String, Object> map = new HashMap<String, Object>();
        Long channelEntityId;
        List<JSONObject> list = new ArrayList<JSONObject>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            channelEntityId = jsonParam.getLong("iEntityId");
        } catch (Exception e) {
            logger.error("根据实体ID,查询其相关代理商和所有门店失败:" + e.getMessage());
            map.put("mes", "入参格式错误");
            JSONObject re = JSONObject.fromObject(map);
            list.add(re);
            return list;
        }

        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
        channelEntityBasicInfo.setChannelEntityId(channelEntityId);
        channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
        List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
        if (0 == channelEntityBasicInfoList.size()) {
            map.put("mes", "没有该实体ID的记录");
            JSONObject re = JSONObject.fromObject(map);
            list.add(re);
            return list;
        }

        Integer channelEntityType = channelEntityBasicInfoList.get(0).getChannelEntityType();
        try {
            List<AgentNodeInfo> agentNodeInfoList = new ArrayList<AgentNodeInfo>();
            if (ChannelConstants.CHANNEL_ENTITY_TYPE_AGENT == channelEntityType) {
                //为代理商
                agentNodeInfoList = agentNodeInfoDao.queryByAgent(channelEntityId);
            } else if (ChannelConstants.CHANNEL_ENTITY_TYPE_NODE == channelEntityType) {
                //为门店
                agentNodeInfoList = agentNodeInfoDao.queryByNode(channelEntityId);
            }
            int i = 0;
            for (AgentNodeInfo agentNodeInfo : agentNodeInfoList) {
//                agentNodeInfo.setChannelEntitySerial(agentNodeInfo.getChannelEntityName());
//                if(0 == i){
//                    agentNodeInfo.setChannelEntityName("1");
//                    i++;
//                }else{
//                    agentNodeInfo.setChannelEntityName("2");
//                }
                JSONObject reJson = JSONObject.fromObject(agentNodeInfo);
                list.add(reJson);
            }
        } catch (Exception e) {
            logger.error("根据实体ID,查询其相关代理商和所有门店失败:" + e.getMessage());
        }

        return list;
    }

    @Override
    public List<JSONObject> get_agentByDistrictId(String param)
            throws Exception {

        Map<String, Object> map = new HashMap<String, Object>();
        Long iDistrictId; //分公司编号
        String strAgentName; //代理商名称  "" - 全部
        Long iAgentType;  //代理商类型  -1 - 全部
        List<JSONObject> list = new ArrayList<JSONObject>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            iDistrictId = jsonParam.getLong("iDistrictId");
            strAgentName = jsonParam.getString("strAgentName");
            iAgentType = jsonParam.getLong("iAgentType");
        } catch (Exception e) {
            logger.error("CRM调渠道接口“获取代理商信息”【接口编码：PT-SH-FS-OI3511】门店失败:" + e.getMessage());
            map.put("data", "入参格式错误");
            JSONObject re = JSONObject.fromObject(map);
            list.add(re);
            return list;
        }
        try {
            List<ChannelEntityBasicInfoDtl> channelEntityBasicInfoList = channelEntityBasicInfoService.getAgentByDistrictId(iDistrictId, strAgentName, iAgentType);
            if (0 == channelEntityBasicInfoList.size()) {
                map.put("data", "");
                JSONObject re = JSONObject.fromObject(map);
                list.add(re);
                return list;
            }

            for (ChannelEntityBasicInfoDtl rps : channelEntityBasicInfoList) {
                JSONObject reJson = JSONObject.fromObject(rps);
                list.add(reJson);
            }
        } catch (Exception e) {
            logger.error("CRM调渠道接口“获取代理商信息”【接口编码：PT-SH-FS-OI3511】", e);
            map.put("data", "");
            JSONObject re = JSONObject.fromObject(map);
            list.add(re);
        }
        return list;
    }

    @Override
    public Map<String, Object> sms_nodeAuthCheck(String param) throws Exception {
        logger.debug("********    sms_nodeAuthCheck  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String nodeAuthId;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            nodeAuthId = jsonParam.getString("nodeAuthId");
        } catch (Exception e) {
            logger.error("检查网点授权编码信息失败：" + e.getMessage());
            logger.debug("********    sms_nodeAuthCheck  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        String e1 = "对不起，您输入的网点授权编号不存在，请重新发送;";
        String e2 = "无此网点授权编码对应的网点信息;";
        String e3 = "无此网点详细信息,";
        String e4 = "无此网点授权编码对应的父渠道编号;";
        if (nodeAuthId.equals("")) {
            throw new Exception("对不起，您输入的网点授权编号不存在，请重新发送;");
        }
        try {
            ChnlNodeAuthorizationInfo chnlNodeAuthorizationInfo = new ChnlNodeAuthorizationInfo();
            chnlNodeAuthorizationInfo.setNodeAuthoriztionId(nodeAuthId);
            //根据网点授权编码查询网点编号
            List<ChnlNodeAuthorizationInfo> chnlNodeAuthorizationInfoList =
                    chnlNodeAuthorizationInfoDao.query(chnlNodeAuthorizationInfo);
            if (0 == chnlNodeAuthorizationInfoList.size()) {
                throw new Exception(e1);
            }
            Long chnlEntId = chnlNodeAuthorizationInfoList.get(0).getNodeId();
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(chnlEntId);
            //根据网点编号查询网点信息
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList =
                    channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (0 == channelEntityBasicInfoList.size()) {
                throw new Exception(e2);
            }
            ChannelNode channelNode = new ChannelNode();
            channelNode.setNodeId(chnlEntId);
            channelNode.setRecStatus(1);
            List<ChannelNode> channelNodeList = channelNodeDao.query(channelNode);
            if (0 == channelNodeList.size()) {
                throw new Exception(e3);
            }
            map.put("nodeId", chnlEntId);
            map.put("nodeName", channelEntityBasicInfoList.get(0).getChannelEntityName());
            String ext4 = "0";
            if (StringUtils.isNotBlank(channelNodeList.get(0).getExt4())) {
                ext4 = channelNodeList.get(0).getExt4();
            }
            map.put("ext4", ext4);

        } catch (Exception e) {
            logger.error("检查网点授权编码信息失败：" + e.getMessage());
            logger.debug("********    sms_nodeAuthCheck  end  ********");
            if (e.getMessage().equals(e1) || e.getMessage().equals(e2)
                    || e.getMessage().equals(e3) || e.getMessage().equals(e4)) {
                throw e;
            }
            throw new Exception("查询失败");
        }
        logger.debug("********    sms_nodeAuthCheck  end  ********");
        return map;
    }

    @Override
    public Map<String, Object> updateChannelNodeUnifyCode(String param) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        String orgId;
        String unifyCode;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            orgId = jsonParam.getString("orgId");
            unifyCode = jsonParam.getString("unifyCode");
            logger.info("更新库存全网统一编码，入参：" + jsonParam.toString());
        } catch (Exception e) {
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        if (orgId.equals("") || unifyCode.equals("")) {
            throw new Exception("传入参数不能为空！");
        }

        ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
        channelOrgAgent.setOrgId(Long.valueOf(orgId));
        List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
        if (channelOrgAgents.size() ==0) {
            //crm通过PT-SH-FS-OI0908接口同步渠道编码给渠道侧时，如果渠道网点还没和这个crm组织绑定过实体对应关系，
            // 由于渠道匹配不到网点，所以无法正常保存编码，需要渠道侧先把编码记录下来，后续绑定实体对应关系时，
            // 再去查编码并关联到网点上保存下来
            logger.info("------------没有匹配d的orgId 开始入库----------------");
            ChannelUnifyCodeInfo channelUnifyCodeInfo = new ChannelUnifyCodeInfo();
            try {
                channelUnifyCodeInfo.setOrgId(Long.valueOf(orgId));
                channelUnifyCodeInfo.setUnifyCode(unifyCode);
                channelUnifyCodeInfo.setDoneDate(new Date());
                channelUnifyCodeInfo.setRecStatus(1);
                channelUnifyCodeInfoDao.insert(channelUnifyCodeInfo);
            }catch (Exception e){
                logger.error("没有匹配d的orgId 入库失败，失败原因是：" + e + " 参数为: " + channelUnifyCodeInfo.toString());
            }
            return map;
        }
        Long channelEntityId = channelOrgAgents.get(0).getAgentId();
        ChannelNode channelNode = new ChannelNode();
        channelNode.setNodeId(channelEntityId);
        channelNode.setUnifyCode(unifyCode);
        channelNode.setDoneDate(new Date());
        channelNodeDao.updateUnifyCode(channelNode);
        //crm将全网统一编码同步过来，
        channelNode.setNodeId(channelEntityId);
        channelNode.setRecStatus(1);
        SPrivData sPrivData = new SPrivData();
        sPrivData.setOpId(999990131L);
        sPrivData.setOrgId(0L);
        List<ChannelNodeDtl> channelNodeList = channelNodeDao.getChannelNodeInfo(channelEntityId);
        ChannelNodeDtl channelNodeDtl = channelNodeService.getChannelNodeInfo(channelEntityId);
        if (channelNodeDtl != null){
            Integer iNodeKind = channelNodeDtl.getNodeKind();
            Integer iNodeType = channelNodeDtl.getNodeType();
             String businessTime = channelNodeDtl.getBusinessTime();
            if (iNodeKind.equals(1) || iNodeKind.equals(2) && (iNodeType.equals(1) || iNodeType.equals(2))){
                if (unifyCode.length() == 19 && com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(businessTime)){
                    JSONObject request1=new JSONObject();
                    ChannelNodeExtinfo channelNodeExtinfo = new ChannelNodeExtinfo();
                    channelNodeExtinfo.setRecStatus(1);
                    channelNodeExtinfo.setNodeId(channelEntityId);
                    List<ChannelNodeExtinfo> channelNodeExtinfoList = channelNodeExtinfoDao.query(channelNodeExtinfo);
                    Double longitude = Double.parseDouble(channelNodeDtl.getLongitude());
                    Double latitude = Double.parseDouble(channelNodeDtl.getLatitude());
                    CoordinateTransformUtil.Coordinate gcj02ToWgs84 = CoordinateTransformUtil.gcj02ToWgs84(longitude,latitude);
                    if (channelNodeExtinfoList.size()>0){
                        request1.put("shopPhone",channelNodeExtinfoList.get(0).getForeignContactNumber());
                    }
                    businessTime = TimesUtils.businessTime(businessTime); //转换时间格式
                    request1.put("unifiedChannelId",unifyCode);
                    request1.put("businessHours",businessTime);
                    request1.put("telNumber",channelNodeDtl.getRel_relationMobile_1());
                    request1.put("Address",channelNodeDtl.getNodeAddr());

                    request1.put("longitude",gcj02ToWgs84.getLongitude());
                    request1.put("latitude",gcj02ToWgs84.getLatitude());
                    request1.put("shortName",channelNodeDtl.getChannelEntityName());
                    String businessScope = convertStyleofManageScope(channelNodeDtl.getManageScope());
                    request1.put("businessScope",businessScope);
                    request1.put("busiRegProvinceCode", "210");

                    String jsonString = "";
                    String params = request1.toString();
                    try {
                        logger.info("同步营业厅时间给云店方法名为：business_hours_joint，入参为：" +  params);
                        jsonString = channelSoaService.call5("business_hours_joint", params);
                        logger.info("同步营业厅时间给云店接口调用成功，" + jsonString);

                    }catch (Exception e){
                        logger.error("调用同步营业厅时间给云店方法名为：business_hours_joint，接口失败，失败原因是：" + e);
                    }finally {
                        if(com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(jsonString)){
                            JSONObject jsonObject = JSONObject.fromObject(jsonString);
                            String respCode = (String) jsonObject.get("respCode");
                            JSONObject jsonObject1 = JSONObject.fromObject(jsonObject);
                            String bizCode = (String) jsonObject1.get("bizCode");
                            //根据响应值判断是否插入定时任务需要扫描的表
                            if (respCode != "00000" && bizCode != "0") {
                                //将数据插入表中
                                BusinessHoursJoint businessHoursJoint = new BusinessHoursJoint();
                                businessHoursJoint.setChannelEntityId(channelEntityId);
                                businessHoursJoint.setBusinessHours(businessTime);
                                businessHoursJoint.setUnifiedChannelId(unifyCode);
                                businessHoursJoint.setState(1); //1代表未成功传输
                                businessHoursJoint.setDoneDate(DateUtil.getCurrDate());
                                businessHoursJointDao.insert(businessHoursJoint);
                                logger.info("同步营业厅时间数据接口应答失败，失败数据记表 BUSINESS_HOURS_JOINT ，数据插入成功！");
                            }
                        }else {
                            //将同步营业厅响应为空 将这部分数据入调度失败表 走 失败机制
                            BusinessHoursJoint businessHoursJoint = new BusinessHoursJoint();
                            businessHoursJoint.setChannelEntityId(channelEntityId);
                            businessHoursJoint.setBusinessHours(businessTime);
                            businessHoursJoint.setUnifiedChannelId(unifyCode);
                            businessHoursJoint.setState(1); //1代表未成功传输
                            businessHoursJoint.setDoneDate(DateUtil.getCurrDate());
                            businessHoursJointDao.insert(businessHoursJoint);
                            logger.info("同步营业厅时间数据响应数据为空导致失败，失败数据记表 BUSINESS_HOURS_JOINT ，数据插入成功！");
                        }
                    }

                }
            }
            ChannelNodeDtl syncChannelNodes=channelNodeList.get(0);
            if (syncChannelNodes.getUnifyCode()!=null){
                ChannelEntityRelationInfo relationInfo = new ChannelEntityRelationInfo();
                relationInfo.setRecStatus(1);
                relationInfo.setRelationType(1);
                relationInfo.setChannelEntityId(syncChannelNodes.getNodeId());
                List<ChannelEntityRelationInfo> relationInfos = channelEntityRelationInfoDao.query(relationInfo);
                if(orgId!=null && relationInfos.size()!=0){
                    crm2ChannelService.SyncChannelData(orgId,relationInfos.get(0).getRelationName(),relationInfos.get(0).getRelationMobile(),syncChannelNodes.getNodeAddr());
                }
            }
            if (channelNodeDtl  != null){
                Integer nodeKind = channelNodeDtl.getNodeKind();
                logger.info("---------根据网点Id查网点信息成功，开始查询开关是否打开-----------");
                List<ChannelSysBaseType> channelSysBaseTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(990099, 2, null, null);
                if (channelSysBaseTypeList.size()> 0){
                    String flag = channelSysBaseTypeList.get(0).getCodeName();
                    //渠道接口和 一级排队 接口调用开关 Y 开 非Y 关
                    if (flag.equals("Y")){
                        logger.info("---------开关为打开状态，开始业务判断是否满足接口调用条件---------");
                        //同步营业厅信息
                        if((nodeKind == 1 || nodeKind == 2) && com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(unifyCode)){
                            logger.info("----------满足业务条件，开始拼装业务参数---------");
                            String areaCode = "138";
                            String areaName = "浦东新区";
                            Integer stationStatus = 1;
                            List<ChannelSysBaseType> sysBaseTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10023, channelNodeDtl.getRegionId(), channelNodeDtl.getDistrictId(), null);
                            if (sysBaseTypeList.size() == 1) {
                                areaCode = sysBaseTypeList.get(0).getExternCode();
                                areaName = sysBaseTypeList.get(0).getCodeName();
                            }
                            if (channelNodeDtl.getChannelEntityStatus() == 11){
                                stationStatus = 1;
                            }else{
                                stationStatus = 2;
                            }
                            String serviceTypesParams = "[{ \"serviceCode\": \"E\",\"serviceName\": \"宽带业务\"}," +
                                    "{ \"serviceCode\": \"H\",\"serviceName\": \"其他业务\"}," +
                                    "{ \"serviceCode\": \"P\",\"serviceName\": \"补换卡\"}," +
                                    "{ \"serviceCode\": \"Q\",\"serviceName\": \"换套餐\"}," +
                                    "{ \"serviceCode\": \"X\",\"serviceName\": \"新开户\"}," +
                                    "{ \"serviceCode\": \"Z\",\"serviceName\": \"増值税\"}]";
                            businessTime = TimesUtils.businessTime(channelNodeDtl.getBusinessTime());
                            logger.info("转换后时间为：" + businessTime);
                            String busitimes[] = businessTime.split(",");
                            businessTime = busitimes[0];
                            JSONObject json = new JSONObject();
                            json.put("provinceCode", "210");            //省编码
                            json.put("provinceName", "上海");            //省名称
                            json.put("cityCode", "210");                //地市编码
                            json.put("cityName", "上海");                //市名称
                            json.put("areaCode", areaCode);                //所在区/县编码
                            json.put("areaName", areaName);                   //所在区/县
                            json.put("stationCode", unifyCode);         //全网渠道编码（19位营业厅编码）
                            json.put("provinceChannelCode", channelNodeDtl.getRegionId());        //省内渠道编码
                            json.put("stationName", channelNodeDtl.getChannelEntityName());                //厅店名称
                            json.put("stationAddr", channelNodeDtl.getNodeAddr());                //营业厅地址
                            json.put("stationMobile", channelNodeDtl.getRel_relationMobile_1());              //厅店联系电话
                            json.put("stationConsigneeName", channelNodeDtl.getRel_relationName_1());       //厅店联系人
                            json.put("longitude", channelNodeDtl.getLongitude());                  //经度
                            json.put("latitude", channelNodeDtl.getLatitude());                   //纬度
                            json.put("workPeriod", businessTime);                 //营业时段
                            json.put("isBusiness", "1");                 //当天是否营业
                            json.put("isAppointment", "1");              //当天是否支持预约
                            json.put("stationStatus", stationStatus);              //营业厅状态
                            json.put("isDelete", "0");                   //是否是删除状态; 默认为0; 当时删除的时候为：1
                            json.put("serviceTypes", JSONArray.fromObject(serviceTypesParams));               //业务列表
                            json.put("mapSystem", "1");                  //经纬度类型

                            try {
                                channelSoaService.call2("sendNodeInfoToPD", json, sPrivData);
                            }catch (Exception e){
                                logger.error("调用接口 PT-SH-FS-OI6163 失败，失败原因是：" + e);
                                throw new Exception("调用接口 PT-SH-FS-OI6163 失败，失败原因是：" + e.getMessage());
                            }
                        }
                    }
                }
            }



        }


        return map;
    }

    @Override
    public Map<String, Object> getChannelAgentTypeInfoOrgId(String param)
            throws Exception {
        logger.debug("********    getChannelAgentTypeInfoOrgId  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String orgId;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            orgId = jsonParam.getString("orgId");
        } catch (Exception e) {
            logger.error("传入的crm组织id异常：" + e.getMessage());
            logger.debug("********    getChannelAgentTypeInfoOrgId  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        if (orgId.equals("")) {
            throw new Exception("对不起，您输入的crm组织Id(orgId)为空，请重新发送;");
        }
        try {
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setOrgId(Long.valueOf(orgId));
            List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgents.size() == 1) {
                map.put("agentId", channelOrgAgents.get(0).getAgentId());
                map.put("orgId", channelOrgAgents.get(0).getOrgId());
            } else {
                map.put("agentId", "");
                map.put("orgId", "");
            }

        } catch (Exception e) {
            logger.error("根据crm的orgId查询对应渠道实体对应关系接口失败：" + e.getMessage());
            logger.debug("********    getChannelAgentTypeInfoOrgId  end  ********");
            throw new Exception("查询失败");
        }
        logger.debug("********    getChannelAgentTypeInfoOrgId  end  ********");
        return map;
    }

    @Override
    public Map<String, Object> getChannelEntityNode(String param)
            throws Exception {
        logger.debug("********    getChannelEntityNode  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        String nodeNameNew = "";
        String nodeNameOld = "";
        String operaType = "";
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            nodeNameNew = jsonParam.getString("nodeNameNew");
            nodeNameOld = jsonParam.getString("nodeNameOld");
            operaType = jsonParam.getString("operaType");
        } catch (Exception e) {
            logger.error("传入的参数异常：" + e.getMessage());
            logger.debug("********    getChannelEntityNode  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        try {
            /**
             * operaType = 1/2
             */
            if (operaType == "") {

            } else {
                //除了搬迁开业
                if (operaType.equals("1") || operaType.equals("2")) {
                    /**
                     * 调用接口，判断是否渠道系统存在
                     */
                    List<ChannelEntityBasicInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.getChannelEntityBasicInfoName(nodeNameNew);
                    if (channelEntityBasicInfos.size() <= 0) {
                        map.put("status", "0");  //不存在
                    } else {
                        map.put("status", "1");  //存在
                    }
                }
                if (operaType.equals("3")) {
                    //需要判断1：新营业厅名称，2：原来营业厅名称。调用查询接口两次
                    //1
                    if (StringUtils.isNotBlank(nodeNameNew) && StringUtils.isNotBlank(nodeNameOld)) {
                        List<ChannelEntityBasicInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.getChannelEntityBasicInfoName(nodeNameNew);
                        if (channelEntityBasicInfos.size() <= 0) {
                            map.put("statusNew", "0");  //不存在
                        } else {
                            map.put("statusNew", "1");  //存在
                        }
                        List<ChannelEntityBasicInfo> channelEntityBasicInfoOlds = channelEntityBasicInfoDao.getChannelEntityBasicInfoName(nodeNameOld);
                        if (channelEntityBasicInfoOlds.size() <= 0) {
                            map.put("statusOld", "0");  //不存在
                        } else {
                            map.put("statusOld", "1");  //存在
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.error("根据新开业、搬迁开业、搬迁停业、营业厅关闭校验渠道网点名称是否存在失败：" + e.getMessage());
            logger.debug("********    getChannelEntityNode  end  ********");
            throw new Exception("查询失败");
        }
        logger.debug("********    getChannelEntityNode  end  ********");
        return map;
    }

    @Override
    public Map<String, Object> operaTypeChannelNodeSynerGy(String param)
            throws Exception {
        logger.debug("********    getChannelEntityNode  begin  ********");
        logger.info("operaTypeChannelNodeSynerGy 入参：" + param);
        Map<String, Object> map = new HashMap<String, Object>();
        String nodeNameNew = "";
        String nodeNameOld = "";
        String beginBusinessDate = "";
        String nodeAdressNew = "";
        String operaType = "";
        String stopNodeDate = "";
        String contractNumber = "";
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            nodeNameNew = jsonParam.getString("nodeNameNew");
            nodeNameOld = jsonParam.getString("nodeNameOld");
            beginBusinessDate = jsonParam.getString("beginBusinessDate");
            nodeAdressNew = jsonParam.getString("nodeAdressNew");
            operaType = jsonParam.getString("operaType");
            stopNodeDate = jsonParam.getString("stopNodeDate");
            contractNumber = jsonParam.getString("contractNumber");
        } catch (Exception e) {
            logger.error("传入的参数异常：" + e.getMessage());
            logger.debug("********    getChannelEntityNode  end  ********");
            throw new Exception("未输入相应参数或传参格式不正确");
        }
        try {
            //根据网点类型进行管理
            if (StringUtils.isNotBlank(operaType)) {
                if (operaType.equals("1")) {//新开业
                    ChannelNodeSynerGy channelNodeSynerGy = new ChannelNodeSynerGy();
                    channelNodeSynerGy.setNodeNameOld(nodeNameOld);
                    channelNodeSynerGy.setCreateDate(DateUtil.getDateUtil(beginBusinessDate, "yyyy-MM-dd HH:mm:ss"));
                    channelNodeSynerGy.setNodeAdress(nodeAdressNew);
                    channelNodeSynerGy.setStopNodeDate(stopNodeDate);
                    channelNodeSynerGy.setNodeStatus(Long.valueOf(operaType));
                    channelNodeSynerGy.setRecStatus(1L);
                    channelNodeSynerGy.setNodeHandle(1L);
                    channelNodeSynerGy.setContractNumber(contractNumber);
                    channelNodeSynerGyDao.insert(channelNodeSynerGy);
//					 crm2ChannelService.getChannelNodeSynerGy(channelNodeSynerGy.getNodeNameNew(),channelNodeSynerGy.getNodeNameOld(),"change",1L,null);
                    map.put("status", "0");
                    map.put("nodeStatus", operaType);
                }
                if (operaType.equals("2")) { //搬迁开业
                    //回复后是新名称，原来的id
                    /**
                     * 1：先查询原来营业厅数据，在进行数据处理,根据原营业厅名称查询
                     */
                    ChannelNodeSynerGy channelNodeSynerGy = new ChannelNodeSynerGy();
                    channelNodeSynerGy.setNodeNameOld(nodeNameOld);
                    List<ChannelNodeSynerGy> channelNodeSynerGies = channelNodeSynerGyDao.query(channelNodeSynerGy);
                    if (channelNodeSynerGies.size() > 0) {

                        ChannelNodeSynerGy channelNodeSynerGy2 = channelNodeSynerGies.get(0);
                        /**
                         * 修改数据前，先将原数据备份到历史表
                         */
                        ChannelNodeSynerGyHis channelNodeSynerGyHis = new ChannelNodeSynerGyHis();
                        channelNodeSynerGyHis.setNodeAdress(channelNodeSynerGy2.getNodeAdress());
                        channelNodeSynerGyHis.setCreateDate(channelNodeSynerGy2.getCreateDate());
                        channelNodeSynerGyHis.setNodeNameNew(channelNodeSynerGy2.getNodeNameNew());
                        channelNodeSynerGyHis.setNodeStatus(channelNodeSynerGy2.getNodeStatus());
                        channelNodeSynerGyHis.setNodeNameOld(channelNodeSynerGy2.getNodeNameOld());
                        channelNodeSynerGyHis.setRecStatus(channelNodeSynerGy2.getNodeStatus());
                        channelNodeSynerGyHis.setNodeHandle(channelNodeSynerGy2.getNodeHandle());
                        channelNodeSynerGyHis.setContractNumber(channelNodeSynerGy2.getContractNumber());
                        channelNodeSynerGyHisDao.insert(channelNodeSynerGyHis);


                        channelNodeSynerGy2.setNodeNameNew(nodeNameNew);
                        channelNodeSynerGy2.setNodeStatus(2L);
                        channelNodeSynerGy2.setNodeHandle(2L);
                        channelNodeSynerGy2.setCreateDate(DateUtil.getDateUtil(beginBusinessDate, "yyyy-MM-dd HH:mm:ss"));
                        channelNodeSynerGy2.setStopNodeDate(stopNodeDate);
                        channelNodeSynerGy2.setContractNumber(contractNumber);
                        channelNodeSynerGyDao.update(channelNodeSynerGy2);

                        map.put("status", "0");
                        map.put("nodeStatus", operaType);
                    }
                    if (channelNodeSynerGies.size() <= 0) {
                        ChannelNodeSynerGy channelNodeSynerGy2 = new ChannelNodeSynerGy();
                        channelNodeSynerGy2.setNodeNameNew(nodeNameNew);
                        channelNodeSynerGy2.setNodeNameOld(nodeNameOld);
                        channelNodeSynerGy2.setNodeAdress(nodeAdressNew);
                        channelNodeSynerGy2.setNodeStatus(2L);
                        channelNodeSynerGy2.setNodeHandle(2L);
                        channelNodeSynerGy2.setRecStatus(1L);
                        channelNodeSynerGy2.setCreateDate(DateUtil.getDateUtil(beginBusinessDate, "yyyy-MM-dd HH:mm:ss"));
                        channelNodeSynerGy2.setStopNodeDate(stopNodeDate);
                        channelNodeSynerGy2.setContractNumber(contractNumber);
                        channelNodeSynerGyDao.insert(channelNodeSynerGy2);
                        map.put("status", "0");
                        map.put("nodeStatus", operaType);
                    } else {
                        map.put("status", "1");
                        map.put("nodeStatus", operaType);
                    }
                }
                if (operaType.equals("3")) { //搬迁停业
                    //搬迁停业，渠道侧改状态
                    /**
                     * 1：先查询原来营业厅数据，在进行数据处理,根据原营业厅名称查询
                     */
                    String nodeName = "";
                    if (StringUtils.isNotBlank(nodeNameNew)) {
                        nodeName = nodeNameNew;
                    } else {
                        nodeName = nodeNameOld;
                    }
                    List<ChannelNodeSynerGy> channelNodeSynerGies = channelNodeSynerGyDao.getChannelNodeSynerGyName(nodeName);
                    if (channelNodeSynerGies.size() > 0) {
                        ChannelNodeSynerGy channelNodeSynerGy2 = channelNodeSynerGies.get(0);
                        /**
                         * 修改数据前，先将原数据备份到历史表
                         */
                        ChannelNodeSynerGyHis channelNodeSynerGyHis = new ChannelNodeSynerGyHis();
                        channelNodeSynerGyHis.setNodeAdress(channelNodeSynerGy2.getNodeAdress());
                        channelNodeSynerGyHis.setCreateDate(channelNodeSynerGy2.getCreateDate());
                        channelNodeSynerGyHis.setNodeNameNew(channelNodeSynerGy2.getNodeNameNew());
                        channelNodeSynerGyHis.setNodeStatus(channelNodeSynerGy2.getNodeStatus());
                        channelNodeSynerGyHis.setNodeNameOld(channelNodeSynerGy2.getNodeNameOld());
                        channelNodeSynerGyHis.setRecStatus(channelNodeSynerGy2.getNodeStatus());
                        channelNodeSynerGyHis.setNodeHandle(channelNodeSynerGy2.getNodeHandle());
                        channelNodeSynerGyHisDao.insert(channelNodeSynerGyHis);

                        channelNodeSynerGy2.setNodeStatus(3L);
                        channelNodeSynerGy2.setNodeHandle(3L);
                        channelNodeSynerGy2.setCreateDate(DateUtil.getDateUtil(beginBusinessDate, "yyyy-MM-dd HH:mm:ss"));
                        channelNodeSynerGy2.setStopNodeDate(stopNodeDate);
                        channelNodeSynerGyDao.update(channelNodeSynerGy2);
                        /**
                         * 将数据推送给crm
                         */
//						crm2ChannelService.getChannelNodeSynerGy(channelNodeSynerGy2.getNodeNameNew(),channelNodeSynerGy2.getNodeNameOld(),"change",1L,null);
                        map.put("status", "0");
                        map.put("nodeStatus", operaType);
                    }
                    if (channelNodeSynerGies.size() <= 0) {
                        ChannelNodeSynerGy channelNodeSynerGy2 = new ChannelNodeSynerGy();
                        channelNodeSynerGy2.setNodeNameNew(nodeNameNew);
                        channelNodeSynerGy2.setNodeNameOld(nodeNameOld);
                        channelNodeSynerGy2.setNodeAdress(nodeAdressNew);
                        channelNodeSynerGy2.setNodeStatus(3L);
                        channelNodeSynerGy2.setNodeHandle(3L);
                        channelNodeSynerGy2.setRecStatus(1L);
                        channelNodeSynerGy2.setCreateDate(DateUtil.getDateUtil(beginBusinessDate, "yyyy-MM-dd HH:mm:ss"));
                        channelNodeSynerGy2.setStopNodeDate(stopNodeDate);
                        channelNodeSynerGyDao.insert(channelNodeSynerGy2);
                        map.put("status", "0");
                        map.put("nodeStatus", operaType);
                    } else {
                        map.put("status", "1");
                        map.put("nodeStatus", operaType);
                    }
                }
                if (operaType.equals("4")) { //营业厅关闭
                    //搬迁停业，渠道侧改状态
                    /**
                     * 1：先查询原来营业厅数据，在进行数据处理,根据原营业厅名称查询
                     */
//					if(StringUtils.isNotBlank(nodeNameNew)){
//						nodeName = nodeNameNew;
//						channelNodeSynerGy.setNodeNameNew(nodeNameNew);
//					}else{
//						nodeName = nodeNameOld;
//						channelNodeSynerGy.setNodeNameOld(nodeNameOld);
//					}
                    List<ChannelNodeSynerGy> channelNodeSynerGies = channelNodeSynerGyDao.getChannelNodeSynerGyName(nodeNameOld);
                    if (channelNodeSynerGies.size() > 0) {
                        ChannelNodeSynerGy channelNodeSynerGy2 = channelNodeSynerGies.get(0);
                        /**
                         * 修改数据前，先将原数据备份到历史表
                         */
                        ChannelNodeSynerGyHis channelNodeSynerGyHis = new ChannelNodeSynerGyHis();
                        channelNodeSynerGyHis.setNodeAdress(channelNodeSynerGy2.getNodeAdress());
                        channelNodeSynerGyHis.setCreateDate(channelNodeSynerGy2.getCreateDate());
                        channelNodeSynerGyHis.setNodeNameNew(channelNodeSynerGy2.getNodeNameNew());
                        channelNodeSynerGyHis.setNodeStatus(channelNodeSynerGy2.getNodeStatus());
                        channelNodeSynerGyHis.setNodeNameOld(channelNodeSynerGy2.getNodeNameOld());
                        channelNodeSynerGyHis.setRecStatus(channelNodeSynerGy2.getNodeStatus());
                        channelNodeSynerGyHis.setNodeHandle(channelNodeSynerGy2.getNodeHandle());
                        channelNodeSynerGyHisDao.insert(channelNodeSynerGyHis);

                        channelNodeSynerGy2.setNodeStatus(4L);
                        channelNodeSynerGy2.setNodeHandle(4L);
                        channelNodeSynerGy2.setCreateDate(DateUtil.getDateUtil(beginBusinessDate, "yyyy-MM-dd HH:mm:ss"));
                        channelNodeSynerGy2.setStopNodeDate(stopNodeDate);
                        channelNodeSynerGyDao.update(channelNodeSynerGy2);

                        /**
                         * 根据原来网点名称获取对应的id
                         */
                        List<ChannelEntityBasicInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.getChannelEntityBasicInfoName(nodeNameOld);

                        if (channelEntityBasicInfos.size() <= 0) {
                            map.put("status", "2");
                            map.put("nodeStatus", operaType);
                            return map;
                        }
//						//将渠道侧网点改成暂停营业
//						ChannelEntityBasicInfo channelEntityBasicInfo = channelEntityBasicInfos.get(0);
//						Long doneCode = channelBusiRecordDao.getSequence();
//						channelEntityBasicInfo.setRecStatus(0);
//						channelEntityBasicInfoDao.update(channelEntityBasicInfo);
//
//						channelEntityBasicInfo.setRecStatus(1);
//						channelEntityBasicInfo.setChannelEntityStatus(13);
//						channelEntityBasicInfo.setDoneCode(doneCode);
//						channelEntityBasicInfoDao.insert(channelEntityBasicInfo);


                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
                        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMM");
                        Date stopNodeDateTime = sdf.parse(stopNodeDate);
                        //将数据保存到临时扫表表，六个月后置为无效
                        ChannelNodeChangeSynerGy channelNodeChangeSynerGy = new ChannelNodeChangeSynerGy();
                        if (channelEntityBasicInfos.size() == 1) {
                            channelNodeChangeSynerGy.setNodeId(channelEntityBasicInfos.get(0).getChannelEntityId());
                        }
                        /**
                         * 如果已经一个一天退出，就不能继续退出了。
                         */
                        ChannelNodeChangeSynerGy changeSynerGy = new ChannelNodeChangeSynerGy();
                        changeSynerGy.setNodeId(channelEntityBasicInfos.get(0).getChannelEntityId());
                        changeSynerGy.setRecStatus(1L);
                        List<ChannelNodeChangeSynerGy> changeSynerGies = channelNodeChangeSynerGyDao.query(changeSynerGy);
                        if (changeSynerGies.size() == 1) {
                            map.put("status", "1");
                            map.put("nodeStatus", operaType);
                            return map;
                        }
                        channelNodeChangeSynerGy.setBillMonth(Long.valueOf(sdf1.format(stopNodeDateTime)));
                        channelNodeChangeSynerGy.setNodeSatus(13L);
                        channelNodeChangeSynerGy.setRecStatus(1L);
                        channelNodeChangeSynerGy.setOpId(999L);
                        channelNodeChangeSynerGy.setOrgId(999L);
                        channelNodeChangeSynerGyDao.insert(channelNodeChangeSynerGy);

                        /**
                         * 将数据推送给crm
                         */
//						crm2ChannelService.getChannelNodeSynerGy(channelNodeSynerGy2.getNodeNameNew(),channelNodeSynerGy2.getNodeNameOld(),"delete",0L,null);
                        map.put("status", "0");
                        map.put("nodeStatus", operaType);
                    }
                    if (channelNodeSynerGies.size() <= 0) {
                        /**
                         * 根据原来网点名称获取对应的id
                         */
                        List<ChannelEntityBasicInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.getChannelEntityBasicInfoName(nodeNameOld);

                        if (channelEntityBasicInfos.size() <= 0) {
                            map.put("status", "2");
                            map.put("nodeStatus", operaType);
                            return map;
                        }
//						//将渠道侧网点改成暂停营业
//						ChannelEntityBasicInfo channelEntityBasicInfo = channelEntityBasicInfos.get(0);
//						Long doneCode = channelBusiRecordDao.getSequence();
//						channelEntityBasicInfo.setRecStatus(0);
//						channelEntityBasicInfoDao.update(channelEntityBasicInfo);
//
//						channelEntityBasicInfo.setRecStatus(1);
//						channelEntityBasicInfo.setChannelEntityStatus(13);
//						channelEntityBasicInfo.setDoneCode(doneCode);
//						channelEntityBasicInfoDao.insert(channelEntityBasicInfo);


                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
                        Date stopNodeDateTime = sdf.parse(stopNodeDate);
                        ChannelNodeChangeSynerGy channelNodeChangeSynerGy = new ChannelNodeChangeSynerGy();
                        if (channelEntityBasicInfos.size() == 1) {
                            channelNodeChangeSynerGy.setNodeId(channelEntityBasicInfos.get(0).getChannelEntityId());
                        }
                        /**
                         * 如果已经一个一天退出，就不能继续退出了。
                         */
                        ChannelNodeChangeSynerGy changeSynerGy = new ChannelNodeChangeSynerGy();
                        changeSynerGy.setNodeId(channelEntityBasicInfos.get(0).getChannelEntityId());
                        changeSynerGy.setRecStatus(1L);
                        List<ChannelNodeChangeSynerGy> changeSynerGies = channelNodeChangeSynerGyDao.query(changeSynerGy);
                        if (changeSynerGies.size() == 1) {
                            map.put("status", "1");
                            map.put("nodeStatus", operaType);
                            return map;
                        }
                        channelNodeChangeSynerGy.setBillMonth(Long.valueOf(sdf1.format(stopNodeDateTime)));
                        channelNodeChangeSynerGy.setNodeSatus(13L);
                        channelNodeChangeSynerGy.setRecStatus(1L);
                        channelNodeChangeSynerGy.setOpId(999L);
                        channelNodeChangeSynerGy.setOrgId(999L);
                        channelNodeChangeSynerGyDao.insert(channelNodeChangeSynerGy);

                        /**
                         * 将数据推送给crm
                         */
//						crm2ChannelService.getChannelNodeSynerGy(channelNodeSynerGy2.getNodeNameNew(),channelNodeSynerGy2.getNodeNameOld(),"delete",0L,null);
                        map.put("status", "0");
                        map.put("nodeStatus", operaType);
                    } else {
                        map.put("status", "1");
                        map.put("nodeStatus", operaType);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("根据新开业、搬迁开业、搬迁停业、营业厅关闭校验渠道网点名称是否存在失败：", e);
            logger.debug("********    getChannelEntityNode  end  ********");
            throw new Exception("查询失败" + e.getMessage());
        }
        logger.debug("********    getChannelEntityNode  end  ********");
        return map;
    }

    @Override
    public List<JSONObject> getChannelNodeSynerGy(String param)
            throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        Long operaType; //分公司编号
        List<JSONObject> list = new ArrayList<JSONObject>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            operaType = jsonParam.getLong("operaType");
        } catch (Exception e) {
            logger.error("CRM/4A调渠道接口“获取协作平台同步数据失败:" + e.getMessage());
            map.put("data", "入参格式错误");
            JSONObject re = JSONObject.fromObject(map);
            list.add(re);
            return list;
        }
        try {
            ChannelNodeSynerGy channelNodeSynerGy = new ChannelNodeSynerGy();

            List<ChannelNodeSynerGy> channelNodeSynerGies = channelNodeSynerGyDao.query(channelNodeSynerGy);


            if (0 == channelNodeSynerGies.size()) {
                map.put("data", "");
                JSONObject re = JSONObject.fromObject(map);
                list.add(re);
                return list;
            }
            /**
             * 转换日期格式
             */
            List<ChannelNodeSynerGyDtl> channelNodeSynerGyDtls = new ArrayList<ChannelNodeSynerGyDtl>();
            for (ChannelNodeSynerGy rps : channelNodeSynerGies) {
                ChannelNodeSynerGyDtl channelNodeSynerGyDtl = new ChannelNodeSynerGyDtl();
                channelNodeSynerGyDtl.setNodeAdress(rps.getNodeAdress());
                channelNodeSynerGyDtl.setNodeNameNew(rps.getNodeNameNew());
                channelNodeSynerGyDtl.setNodeNameOld(rps.getNodeNameOld());
                channelNodeSynerGyDtl.setNodeStatus(rps.getNodeStatus());
                if (rps.getCreateDate() == null || "".equals(rps.getCreateDate())) {
                    channelNodeSynerGyDtl.setCreateDate(null);
                } else {
                    channelNodeSynerGyDtl.setCreateDate(DateUtil.getExpireDate(rps.getCreateDate()));
                }
                channelNodeSynerGyDtl.setStopNodeDate(rps.getStopNodeDate());
                channelNodeSynerGyDtls.add(channelNodeSynerGyDtl);
            }
            for (ChannelNodeSynerGyDtl rps : channelNodeSynerGyDtls) {
                JSONObject reJson = JSONObject.fromObject(rps);
                list.add(reJson);
            }
        } catch (Exception e) {
            logger.error("CRM/4A调渠道接口“获取协作平台同步数据失败:", e);
            map.put("data", "");
            JSONObject re = JSONObject.fromObject(map);
            list.add(re);
        }
        return list;
    }


    @Override
    public List<Map<String, Object>> checkZydPhone(String param) throws Exception {
        String code = "";
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            code = jsonParam.get("code") == null ? "" : jsonParam.get("code").toString();
        } catch (Exception e) {
            logger.error("入参不正确", e);
            throw new Exception("入参不正确");
        }
        if (code.equals("")) {
            throw new Exception("入参必须包含code。用户的code为必传项!");
        }


        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        // 根据login_name 查询 channel_Zyd_Phone_Check 表

        // select b.bill_id, b.mac_id, b.node_id, b.expire_date, b.user_name
        //   from channel_Zyd_Phone_Check b
        //  where b.rec_status = 1
        //    and b.login_name = 'B060607'
        List<ChannelZydPhoneCheck> channelZydPhoneChecks = channelZydPhoneCheckDao.queryByLoginName(code);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        for (ChannelZydPhoneCheck channelZydPhoneCheck : channelZydPhoneChecks) {
            Map<String, Object> mapList = new HashMap<String, Object>();
            mapList.put("bill_id", channelZydPhoneCheck.getBillId());
            mapList.put("mac_id", channelZydPhoneCheck.getMacId());
            mapList.put("node_id", channelZydPhoneCheck.getNodeId());
            mapList.put("expire_date", df.format(channelZydPhoneCheck.getExpireDate()));
            mapList.put("user_name", channelZydPhoneCheck.getUserName());
            list.add(mapList);
        }
        return list;
    }

    /**
     * 市场-2016-9019 二维码需求处理及查询优化-代理商管理
     * 根据orgId查询网店类型
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, Object> getNodeKind(String param) throws Exception {
        Long orgId;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            orgId = jsonParam.get("orgId") == null ? null : Long.valueOf(jsonParam.get("orgId").toString());
        } catch (Exception e) {
            logger.error("入参不正确", e);
            throw new Exception("入参不正确");
        }
        if (orgId == null) {
            throw new Exception("入参必须包含orgId");
        }

        List<Map<String, Object>> list = channelNodeDao.getNodeKindByOrgId(orgId);

        if (list.size() > 0 && list.get(0) != null) {
            return list.get(0);
        } else {
            throw new Exception("根据orgId未查询到对应的网点！请确认网点是否已退出或无实体对应关系！");
        }
    }

    /**
     * 根据代理商手机号，查询网点名称，网点渠道类型，网点渠道归属
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public List<ChannelNodeInfo> get_channelNodeInfoBySvrNum(String param) throws Exception {
        String bill = null;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            bill = jsonParam.getString("code");
        } catch (Exception e) {
            logger.error("入参不正确", e);
            throw new Exception("入参不正确");
        }
        List<ChannelNodeInfo> list = channelNodeDao.getchannelNodeInfoBySvrNum(bill);
        List<ChannelNodeInfo> list2 = new ArrayList<ChannelNodeInfo>();
        if (list.size() > 0 && list.get(0) != null) {
            list2.add(list.get(0));
        }
        return list2;
    }

    @Override
    public Map<String, Object> checkSellResourceSyn(String param) throws Exception {
        List<SellResourceSync> successSellResourceSyncList = sellResourceSyncDao.getSuccessSellResourceInfoLastDate();
        List<SellResourceSync> failureSellResourceSyncList = sellResourceSyncDao.getFailureSellResourceInfoLastDate();

        Map<String, Object> reMap = new HashMap<String, Object>();
        reMap.put("successData", successSellResourceSyncList);
        reMap.put("failureData", failureSellResourceSyncList);
        return reMap;
    }

    /***
     * 公安反诈骗查，根据orgId查询对应的网点信息
     * @param param
     */
    @Override
    public Map<String, Object> getChannelNode(String param) {
        logger.info("公安反诈骗查询，根据orgId查询对应接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        String orgId = "";
        ChannelOrgAgent channelOrgAgent2 = null;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            orgId = jsonParam.getString("orgId");

        } catch (Exception e) {
            logger.error("获取入参参数：公安反诈骗查询，根据orgId查询对应接口失败:", e);
            logger.debug("********    公安反诈骗查询，根据orgId查询对应接口  结束  ********");
            map.put("error", "入参格式错误");
            return map;
        }
        try {
            //1根据orgId查询实体对应关系
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setOrgId(Long.valueOf(orgId));
            List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgents.size() <= 0) {
                map.put("channelEntityId", "");
                map.put("channelEntityName", "");
                map.put("nodeAddr", ""); //门店地址
                map.put("foreignContactNumber", ""); //对外联系电话
                map.put("Rel_relationMobile_1", "");//厅经理联系电话
                map.put("resultMsg", "根据orgId未查询到实体对应关系数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() > 1) {
                map.put("channelEntityId", "");
                map.put("channelEntityName", "");
                map.put("nodeAddr", ""); //门店地址
                map.put("foreignContactNumber", ""); //对外联系电话
                map.put("Rel_relationMobile_1", "");//厅经理联系电话
                map.put("resultMsg", "根据orgId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() == 1) {
                channelOrgAgent2 = channelOrgAgents.get(0);
            }
            //2查询获取网点信息
            long nodeId = channelOrgAgent2.getAgentId();
            List<ChannelNodeDtl> channelNodeDtlList = channelNodeDao.getChannelNodeInfo(nodeId);
            if (channelNodeDtlList.size() == 1) {
                ChannelNodeDtl channelNodeDtl = channelNodeDtlList.get(0);
                // 厅经理信息
                List<ChannelEntityRelationInfo> channelEntityRelationInfoList = channelEntityRelationInfoDao.getChannelEntityRelationInfo(nodeId, 1);
                //channelNodeDtl.setRel_relationType_1(1);
                //channelNodeDtl.setRel_relationId_1(channelEntityRelationInfoList.get(0).getRelationId());
                //channelNodeDtl.setRel_relationName_1(channelEntityRelationInfoList.get(0).getRelationName());
                channelNodeDtl.setRel_relationMobile_1(channelEntityRelationInfoList.get(0).getRelationMobile());
                //channelNodeDtl.setRel_email_1(channelEntityRelationInfoList.get(0).getEmail());
                map.put("channelEntityId", channelNodeDtl.getChannelEntityId());
                map.put("channelEntityName", channelNodeDtl.getChannelEntityName());
                map.put("nodeAddr", channelNodeDtl.getNodeAddr()); //门店地址
                map.put("foreignContactNumber", channelNodeDtl.getForeignContactNumber()); //对外联系电话
                map.put("Rel_relationMobile_1", channelNodeDtl.getRel_relationMobile_1());//厅经理联系电话
            } else {
                map.put("channelEntityId", "");
                map.put("channelEntityName", "");
                map.put("nodeAddr", ""); //门店地址
                map.put("foreignContactNumber", ""); //对外联系电话
                map.put("Rel_relationMobile_1", "");//厅经理联系电话
                map.put("resultMsg", "网点信息查询失败，根据nodeId：" + nodeId + "查询到的网点数为" + channelNodeDtlList.size());
            }
        } catch (Exception e) {
            logger.error("公安反诈骗查询，根据orgId查询对应接口失败:", e);
            map.put("error", "入参参数类型错误");
            return map;
        }
        logger.info("公安反诈骗查询，根据orgId查询对应接口失败 结束");
        return map;
    }

    public Map<String, Object> getServicePhone1(String param) {
        logger.info("指定服务号码1查询，根据授权编号查询对应接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        String RelationMobile = "";
        String nodeAuthoriztionId = "";
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            nodeAuthoriztionId = jsonParam.getString("nodeAuthoriztionId");
        } catch (Exception e) {
            logger.error("获取入参参数：根据授权编号查询对应接口失败:", e);
            logger.debug("********    指定服务号码1查询，根据orgId查询对应接口  结束  ********");
            map.put("error", "入参格式错误");
            return map;
        }
        try {
            ChnlNodeAuthorizationInfo chnlNodeAuthorizationInfo = new ChnlNodeAuthorizationInfo();
            chnlNodeAuthorizationInfo.setNodeAuthoriztionId(nodeAuthoriztionId);
            List<ChnlNodeAuthorizationInfo> chnlNodeAuthorizationInfos = chnlNodeAuthorizationInfoDao.query(chnlNodeAuthorizationInfo);
            if (chnlNodeAuthorizationInfos.size() != 1) {
                logger.error("该授权编号查询到的网点id不存在或者存在多个");
                throw new Exception("该授权编号查询到的网点id不存在或者存在多个");
            }
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(chnlNodeAuthorizationInfos.get(0).getNodeId());
            channelEntityBasicInfo.setRecStatus(1);
            channelEntityBasicInfo.setChannelEntityType(2);
            List<ChannelEntityBasicInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (channelEntityBasicInfos.size() != 1) {
                logger.error("该网点id查询到的网点不存在或者存在多个");
                throw new Exception("该网点id查询到的网点不存在或者存在多个");
            }
            ChannelEntityRelInfo channelEntityRelInfo = new ChannelEntityRelInfo();
            channelEntityRelInfo.setChannelEntityId(channelEntityBasicInfos.get(0).getChannelEntityId());
            channelEntityRelInfo.setRecStatus(1);
            List<ChannelEntityRelInfo> channelEntityRelInfos = channelEntityRelInfoDao.query(channelEntityRelInfo);
            if (channelEntityRelInfos.size() != 1) {
                logger.error("该网点id查询到的网点父代理商id不存在或者存在多个");
                throw new Exception("该网点id查询到的父代理商id不存在或者存在多个");
            }
            ChannelEntityBasicInfo channelEntityBasicInfo1 = new ChannelEntityBasicInfo();
            channelEntityBasicInfo1.setChannelEntityId(channelEntityRelInfos.get(0).getParentEntity());
            channelEntityBasicInfo1.setRecStatus(1);
            channelEntityBasicInfo1.setChannelEntityType(1);
            List<ChannelEntityBasicInfo> channelEntityBasicInfos1 = channelEntityBasicInfoDao.query(channelEntityBasicInfo1);
            if (channelEntityBasicInfos1.size() != 1) {
                logger.error("该父代理商id查询到的代理商不存在或者存在多个或者无效");
                throw new Exception("该父代理商id查询到的代理商不存在或者存在多个或者无效");
            }
            ChannelEntityRelationInfo channelEntityRelationInfo = new ChannelEntityRelationInfo();
            channelEntityRelationInfo.setChannelEntityId(channelEntityBasicInfos1.get(0).getChannelEntityId());
            channelEntityRelationInfo.setRelationType(ChannelConstants.RELATION_TYPE_SERVICE_NUMBERS_CONTACTS);
            channelEntityRelationInfo.setRecStatus(1);
            List<ChannelEntityRelationInfo> channelEntityRelationInfos = channelEntityRelationInfoDao.query(channelEntityRelationInfo);
            if (channelEntityRelationInfos.size() != 1) {
                logger.error("该代理商id查询到的服务指定号码1,不存在或者存在多个或者无效");
                throw new Exception("该代理商id查询到的服务指定号码1,不存在或者存在多个或者无效");
            }
            RelationMobile = channelEntityRelationInfos.get(0).getRelationMobile();
            map.put("RelationMobile", RelationMobile);
        } catch (Exception e) {
            logger.error("指定服务号码1查询，根据授权编号查询对应接口失败:", e);
            map.put("error", "入参参数类型错误");
            return map;
        }
        logger.info("指定服务号码1查询 ，根据授权编号查询对应接口成功 结束");
        return map;
    }

    /**
     * 同步渠道新系统添加网点
     */
    @Override
    public Map<String, Object> addChannelNode(String param) {
        logger.info("同步渠道新系统 添加网点 接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        SPrivData sPrivData = null;
        ChannelNodeDtl channelNodeDtl = null;
        JSONObject jsonParam = JSONObject.fromObject(param);
        String busiCode = jsonParam.getString("busiCode");
        if ("get-AgentSerial".equals(busiCode)) {
            map = this.getAgentSerial(jsonParam);
        }
        if ("Add-agent".equals(busiCode)) {
            map = this.addAgentInfo(jsonParam);
        }
        if ("upd-agent".equals(busiCode)) {
            map = queryAgentinfo(jsonParam);
            if (ChannelConstants.RETURN_SUCCESS.equals(map.get("resultCode"))) {
                map = updAgentinfo(jsonParam);
            }
        }
        if ("Del-agent".equals(busiCode)) {
            map = this.delAgentinfo(jsonParam, false);
        }
        if ("preDel-agent".equals(busiCode)) {
            map = this.delAgentinfo(jsonParam, true);
        }
        if ("get-UnifyCode".equals(busiCode)) {
            map = getUnifyCode(jsonParam);
        }
        if ("del-wangdian".equals(busiCode)) {
            map = this.delChannelNode(jsonParam);
        }
        if ("upd-wangdian".equals(busiCode)) {
            map = queryChannelNodeByChanneId(jsonParam);
            if (!"-1".equals(map.get("resultCode"))) {
                map = updateChannelNodeInfo(jsonParam);
            }
        }
        if ("Add-wangdian".equals(busiCode)) {
            try {
                String sPrivDataStr = jsonParam.getString("sPrivData");
                String channelNodeDtlStr = jsonParam.getString("channelNodeDtl");
                sPrivData = (SPrivData) JSONObject.toBean(JSONObject.fromObject(sPrivDataStr), SPrivData.class);
                JSONObject jsonOb = JSONObject.fromObject(channelNodeDtlStr);
                channelNodeDtl = (ChannelNodeDtl) JSONObject.toBean(jsonOb, ChannelNodeDtl.class);
                //时间类型转换
                changeDateForm(jsonOb, channelNodeDtl);

            } catch (Exception e) {
                logger.error("获取入参参数：同步渠道新系统 添加网点接口失败:", e);
                logger.debug("********    同步渠道新系统 添加网点 接口 结束  ********");
                map.put("error", "入参格式错误");
                map.put("resultCode", "-1");
                logger.info("同步渠道新系统 接口 结束");
                return map;
            }
            try {
                Integer iRet = channelNodeService.addChannelNodeInfo(sPrivData, channelNodeDtl);
                map.put("resultCode", iRet.toString());
            } catch (Exception e) {
                logger.error("同步渠道新系统 添加网点接口失败:", e);
                map.put("error", e.getMessage());
                map.put("resultCode", "-1");
            }
        }
        logger.info("同步渠道新系统 接口 结束");
        return map;
    }

    @Override
    public List<Map<String, Object>> getChannelEntityInfo(String param) {
        List<Map<String, Object>> data = new ArrayList<Map<String, Object>>();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<JSONObject> hallInfoList = new ArrayList<JSONObject>();
        List<ChannelShidianInfo> channelShidianInfoList = new ArrayList<ChannelShidianInfo>();
        List<ChannelEntityBasicInfo> channelEntityInfos = new ArrayList<ChannelEntityBasicInfo>();
        List<ChannelNode> nodeInfos = new ArrayList<ChannelNode>();
        ArrayList dataList = new ArrayList();
        ChannelShidianInfo channelShidianInfo = new ChannelShidianInfo();
        String syncDate = "";
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            syncDate = jsonParam.getString("syncDate");//调用时间
        } catch (Exception e) {
            logger.error("获取入参参数：根据营业厅名称查询对应接口失败:", e);
            dataMap.put("bizCode", "0001");
            dataMap.put("bizDesc", "入参格式错误");
            if (hallInfoList.size() != 0) {
                dataMap.put("hallInfoList", hallInfoList);
            }
            data.add(dataMap);
            return data;
        }

        try {
            channelShidianInfoList = channelShidianInfoDao.query(channelShidianInfo);

            for (int i = 0; i < channelShidianInfoList.size(); i++) {
                String channelEntityName = "";
                String longitude = "";
                String latitude = "";
                String nodeAddr = "";
                Long channelEntityId = 0L;
                String relationName = "";
                String relationMobile = "";
                String email = "";
                int reginId = 0;
                String reginName = "";
                String businessTime = "";
                Long orgId = 0L;
                String reginCode = "";
                String monday = "";
                String tuesday = "";
                String wednesday = "";
                String thursday = "";
                String friday = "";
                String saturday = "";
                String sunday = "";
                String serviceIds = "";
                String serviceNames = "";
                String flag = "";
                String unifyCode = "";
                Integer label = 0;
                /**
                 * 1.查询配置的试点营业厅，获取其channelEntityId
                 * */
                channelEntityId = channelShidianInfoList.get(i).getChannelEntityId();
                channelEntityInfos = channelEntityBasicInfoDao.getChannelEntityBasicInfoId(String.valueOf(channelEntityId));
                if (channelEntityInfos.size() != 1) {
                    logger.error("该营业厅名称查询到信息,不存在或者存在多个或者无效");
                    continue;
                }
                channelEntityName = channelEntityInfos.get(0).getChannelEntityName();
                reginId = channelEntityInfos.get(0).getRegionId();
                label = channelEntityInfos.get(0).getLabel();

                List<ChannelSysBaseType> channelSysBaseTypeList1 =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10023, reginId, null, null);
                //区名称
                reginName = channelSysBaseTypeList1.get(0).getCodeName();
                nodeInfos = channelNodeDao.getNodeInfo(channelEntityId);
                //经纬度
                if (nodeInfos.size() == 1 && nodeInfos.get(0).getLongitude() != null) {
                    longitude = nodeInfos.get(0).getLongitude();
                }
                if (nodeInfos.size() == 1 && nodeInfos.size() == 1 && nodeInfos.get(0).getLatitude() != null) {
                    latitude = nodeInfos.get(0).getLatitude();
                }
                //营业厅地址
                if (nodeInfos.size() == 1 && nodeInfos.get(0).getNodeAddr() != null) {
                    nodeAddr = nodeInfos.get(0).getNodeAddr();
                }
                //营业时间
                if (nodeInfos.size() == 1 && nodeInfos.get(0).getBusinessTime() != null) {
                    businessTime = nodeInfos.get(0).getBusinessTime();
                }
                //接受转换格式后的营业时间
                List<String> timeList = convertTime(businessTime);
                sunday = timeList.get(0);
                monday = timeList.get(1);
                tuesday = timeList.get(2);
                wednesday = timeList.get(3);
                thursday = timeList.get(4);
                friday = timeList.get(5);
                saturday = timeList.get(6);
                Long agentId = channelEntityId;
                List<ChannelOrgAgent> channelOrgs = channelOrgAgentDao.getChannelOrgAgentAgentId(agentId);
                if (channelOrgs.size() == 1 && channelOrgs.get(0).getOrgId() != null) {
                    orgId = channelOrgs.get(0).getOrgId();
                }
                List<ChannelEntityRelationInfo> channelEntityRelationInfos = channelEntityRelationInfoDao.getChannelEntityRelationInfo(channelEntityId, 1);
                if (channelEntityRelationInfos.size() == 1 && channelEntityRelationInfos.get(0).getRelationName() != null) {
                    relationName = channelEntityRelationInfos.get(0).getRelationName();
                }
                if (channelEntityRelationInfos.size() == 1 && channelEntityRelationInfos.get(0).getRelationMobile() != null) {
                    relationMobile = channelEntityRelationInfos.get(0).getRelationMobile();
                }
                if (channelEntityRelationInfos.size() == 1 && channelEntityRelationInfos.get(0).getEmail() != null) {
                    email = channelEntityRelationInfos.get(0).getEmail();
                }

                List<ChannelSysBaseType> channelSysBaseTypeList3 =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10521, null, null, null);
                serviceIds = "";
                serviceNames = "";
                for (ChannelSysBaseType channelSysBaseType3 : channelSysBaseTypeList3) {
                    serviceIds += channelSysBaseType3.getCodeName() + ",";
                    serviceNames += channelSysBaseType3.getCodeNameNls() + ",";
                }
                /**
                 * 2.如果doneDate字段为空则说明，是第一次调用该接口
                 * */
                //unifyCode（值不会发生改变）通过调用CRM侧接口查询获得，可能存在接口超时的风险；
                //因此第一次调用接口时将unifyCode配置在表里，后续则调用接口获得
                if (channelShidianInfoList.get(i).getUnifyCode() != null) {
                    unifyCode = channelShidianInfoList.get(i).getUnifyCode();
                } else {
                    //一级19位全网统一编码
                    try {
                        unifyCode = this.queryCRMUnifyCode(channelEntityId, orgId);
                    } catch (Exception e) {
                        unifyCode = "";
                    }
                }
                if (!"".equals(unifyCode)) {
                    reginCode = unifyCode.substring(7, 9);
                } else {
                    reginCode = "";
                }
                if (channelShidianInfoList.get(i).getDoneDate() == null) {
                    flag = "01";
                    List<JSONObject> dataList1 = new ArrayList<JSONObject>();
                    dataList1 = insertReturnMsg(channelEntityName, monday, tuesday, wednesday, thursday, friday, saturday, sunday, unifyCode, reginName, reginCode,
                            longitude, latitude, nodeAddr, relationName, relationMobile, email, flag, serviceIds, serviceNames, label);

                    hallInfoList.add(dataList1.get(0));
                    /**
                     * 3.第一次调用完后要把试点信息表中的相应信息填写完整
                     * */
                    updateShidianInfo(channelEntityId, channelEntityName, monday, tuesday, wednesday, thursday, friday, saturday, sunday, unifyCode, reginName, reginCode,
                            longitude, latitude, nodeAddr, relationName, relationMobile, email, serviceIds, syncDate, serviceNames);
                } else if (channelShidianInfoList.get(i).getDoneDate() != null && channelShidianInfoList.get(i).getRecStatus() == 0) {
                    /**
                     * 4.退出试点营业厅时，返回参数如下
                     * */
                    Map<String, Object> hallInfoList1 = new HashMap<String, Object>();
                    flag = "02";
                    List<JSONObject> dataList2 = new ArrayList<JSONObject>();
                    dataList2 = insertReturnMsg(channelEntityName, monday, tuesday, wednesday, thursday, friday, saturday, sunday, unifyCode, reginName, reginCode,
                            longitude, latitude, nodeAddr, relationName, relationMobile, email, flag, serviceIds, serviceNames, label);
                    hallInfoList.add(dataList2.get(0));
                } else if (channelShidianInfoList.get(i).getDoneDate() != null && channelShidianInfoList.get(i).getRecStatus() == 1) {
                    /**
                     * 5.试点营业厅的信息发生修改时，会将配置表的信息和库中的营业厅信息比对
                     * */
                    List<ChannelShidianInfo> changeList = new ArrayList<ChannelShidianInfo>();
                    Map<String, Object> hallInfoList1 = new HashMap<String, Object>();
                    Map<String, Object> dataList1 = new HashMap<String, Object>();
                    changeList = checkIsUpdate(channelEntityId, monday, tuesday, wednesday, thursday, friday, saturday, sunday, reginName, reginCode,
                            longitude, latitude, nodeAddr, relationName, relationMobile, email, serviceIds, serviceNames, label);
                    /**
                     * 6.比对后，将有变动的这条的完整信息存入返回体中，返回如下
                     * */
                    if (changeList.get(0).getDoneDate() != null) {
                        flag = "03";
                        ChannelShidianInfo channelShidianInfo1 = convertToEntity(channelEntityName, monday, tuesday, wednesday, thursday, friday, channelEntityId,
                                saturday, sunday, unifyCode, reginName, reginCode, longitude, latitude, nodeAddr, relationName, relationMobile, email,
                                serviceIds, serviceNames, label);

                        List<JSONObject> data2 = convertChangeList(channelShidianInfo1, flag, syncDate);
                        hallInfoList.add(data2.get(0));
                    }
                }
            }
            dataMap.put("bizCode", "0000");
            dataMap.put("bizDesc", "");
            if (hallInfoList.size() != 0) {
                dataMap.put("hallInfoList", hallInfoList);
            }
            data.add(dataMap);
        } catch (Exception e) {
            logger.error("根据营业厅名称查询相关信息异常:", e);
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "查询渠道库信息时出现异常！！");
            if (hallInfoList.size() != 0) {
                dataMap.put("hallInfoList", hallInfoList);
            }
            data.add(dataMap);
            return data;
        }
        return data;
    }

    @Override
    public List<Map<String, Object>> getCardInfoList(String param) throws Exception {
        String pk = "";
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<JSONObject> cardInfoList = new ArrayList<JSONObject>();
        List<Map<String, Object>> data = new ArrayList<Map<String, Object>>();
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            pk = jsonParam.getString("pk");//调用时间
            List<AuthorizationCardInfo> list = authorizationCardInfoDao.getAuthorizationInfo(pk, null, null, null, null);
            if (list != null && list.size() == 1) {
                Map<String, Object> map = new HashMap<String, Object>();
                AuthorizationCardInfo authorizationCardInfo = list.get(0);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                authorizationCardInfo.setEndDateString(simpleDateFormat.format(authorizationCardInfo.getEndDate()));
                authorizationCardInfo.setStartDateString(simpleDateFormat.format(authorizationCardInfo.getStartDate()));
                map.put("nodeId", authorizationCardInfo.getNodeId());
                map.put("nodeName", authorizationCardInfo.getNodeName());
                map.put("address", authorizationCardInfo.getAddress());
                map.put("startDate", authorizationCardInfo.getStartDateString());
                map.put("endDate", authorizationCardInfo.getEndDateString());
                map.put("companyName", authorizationCardInfo.getCompanyName());
                //转为json格式
                JSONObject jsonObject = JSONObject.fromObject(map);
                cardInfoList.add(jsonObject);
                dataMap.put("bizCode", "0000");//返回码（0000：成功）
                dataMap.put("bizDesc", "数据查询成功");
                dataMap.put("cardInfos", cardInfoList);
            } else {
                dataMap.put("bizCode", "0001");//返回码（0001：失败）
                dataMap.put("bizDesc", "未在库中查询到相应信息！！！");
            }
        } catch (Exception e) {
            logger.error("根据营业厅名称查询相关信息异常:", e);
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "查询渠道库信息时出现异常！！");
        }
        data.add(dataMap);
        return data;
    }

    @Override
    public Map<String, Object> getNodeLevelAndLatitudes(String param) throws Exception {
        logger.info("根据CRM侧的orgId查询对应接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        String orgId = "";
        ChannelOrgAgent channelOrgAgent2 = null;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            orgId = jsonParam.getString("orgId");

        } catch (Exception e) {
            logger.error("获取入参参数：根据orgId查询对应接口失败:", e);
            logger.debug("********    根据orgId查询对应接口  结束  ********");
            map.put("error", "入参格式错误");
            return map;
        }
        try {
            //1根据orgId查询实体对应关系
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setOrgId(Long.valueOf(orgId));
            List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgents.size() <= 0) {
                map.put("resultMsg", "根据orgId未查询到实体对应关系数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() > 1) {
                map.put("resultMsg", "根据orgId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() == 1) {
                channelOrgAgent2 = channelOrgAgents.get(0);
            }
            //2查询获取网点信息
            long nodeId = channelOrgAgent2.getAgentId();
            Integer regionId = null;
            String regionName = "";
            String channelStar = "0";//默认为1星
            String longitude = "";
            String latitude = "";
            String relationName = "";
            String relationMobile = "";
            String stateEncoding = "";
            String geographicalPosition = "";
            String natureOfProperty = "";
            String shopArea = "";
            String geographicalPositionType = "";
            String rentAmount = "";
            String subDivision = "";
            String terminalSalesFormat = "";
            String managementStyle = "";
            String cooperationType = "";
            String businessPatterns = "";
            String industryPatterns = "";
            String industryPatternsAttrs = "";
            String panChannelType = "";
            String panChannelExpansionMode = "";
            List<ChannelNode> channelNodeList = channelNodeDao.getNodeInfo(nodeId);
            if (channelNodeList.size() == 1) {
                ChannelNode channelNode = channelNodeList.get(0);
                longitude = channelNode.getLongitude() == null ? "" : channelNode.getLongitude();
                if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(longitude)) {
                    BigDecimal bigDecimal = new BigDecimal(longitude);
                    longitude = bigDecimal.setScale(6, BigDecimal.ROUND_HALF_UP).toString();
                }
                latitude = channelNode.getLatitude() == null ? "" : channelNode.getLatitude();
                if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(latitude)) {
                    BigDecimal bigDecimal = new BigDecimal(latitude);
                    latitude = bigDecimal.setScale(6, BigDecimal.ROUND_HALF_UP).toString();
                }
                if (channelNode.getSumArea() != null) {
                    BigDecimal bigDecimal = new BigDecimal(channelNode.getSumArea());
                    shopArea = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                }
                if (channelNode.getPropertyNature() != null) {
                    if (channelNode.getPropertyNature() == 1) {
                        natureOfProperty = "1";
                    } else if (channelNode.getPropertyNature() == 2 || channelNode.getPropertyNature() == 3) {
                        natureOfProperty = "2";
                    }
                }
                if (channelNode.getAddressType() != null) {
                    if (channelNode.getAddressType() == 0) {
                        geographicalPosition = "1";
                    } else if (channelNode.getAddressType() == 1) {
                        geographicalPosition = "2";
                    } else if (channelNode.getAddressType() == 2) {
                        geographicalPosition = "3";
                    } else if (channelNode.getAddressType() == 3) {
                        geographicalPosition = "4";
                    }
                }
                geographicalPositionType = channelNode.getGeographicalPositionType() == null ? "" : channelNode.getGeographicalPositionType().toString();
                rentAmount = channelNode.getRentAmount() == null ? "" : channelNode.getRentAmount().toString();
                subDivision = channelNode.getSubDivision() == null ? "" : channelNode.getSubDivision().toString();
                terminalSalesFormat = channelNode.getTerminalSalesFormat() == null ? "" : channelNode.getTerminalSalesFormat().toString();
                managementStyle = channelNode.getManagementStyle() == null ? "" : channelNode.getManagementStyle().toString();
                cooperationType = channelNode.getCooperationType() == null ? "" : channelNode.getCooperationType().toString();
                businessPatterns = channelNode.getBusinessPatterns() == null ? "" : channelNode.getBusinessPatterns().toString();
                industryPatterns = channelNode.getIndustryPatterns() == null ? "" : channelNode.getIndustryPatterns().toString();
                industryPatternsAttrs = channelNode.getIndustryPatternsAttrs() == null ? "" : channelNode.getIndustryPatternsAttrs();
                /*try {
                    if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(industryPatternsAttrs)) {
                        logger.info("本地转换行业形态属性字段的样式开始=================");
                        industryPatternsAttrs = convertStyleofindustryPatternsAttrs(industryPatternsAttrs);
                    }
                    logger.info("本地转换行业形态属性字段的样式成功=================");
                } catch (Exception e) {
                    logger.error("本地做转换行业形态属性的时候出错，请仔细核对！");
                }*/
                String accreditExt4 = channelNode.getExt4() == null ? "1" : channelNode.getExt4();//是否授权网点,默认值为1
                panChannelType = channelNode.getPanChannelType() == null ? "" : channelNode.getPanChannelType().toString();
                panChannelExpansionMode = channelNode.getPanChannelExpansionMode() == null ? "" : channelNode.getPanChannelExpansionMode().toString();
                ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
                channelEntityBasicInfo.setChannelEntityId(nodeId);
                List<ChannelEntityBasicInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                if ((!CollectionUtils.isEmpty(channelEntityBasicInfos)) && (channelEntityBasicInfos.size() == 1)) {
                    Integer channelEntityStatus = channelEntityBasicInfos.get(0).getChannelEntityStatus();
                    regionId = channelEntityBasicInfos.get(0).getRegionId();
                    //值为1--正常；8---预终止
                    if (channelEntityStatus == 11) {
                        stateEncoding = "1";
                    } else {
                        stateEncoding = "2";
                    }
                }
                List<ChannelSysBaseType> channelSysBaseTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(1205, channelNode.getNodeKind(), null, null);
                Map<String, String> nameMap = ChannelSysBaseTypeUtil.codeNameMap(10023);
                if (!nameMap.isEmpty()) {
                    regionName = nameMap.get(regionId.toString());
                }
                if (!channelSysBaseTypeList.isEmpty()) {
                    if (channelSysBaseTypeList.size() == 1) {
                        channelStar = channelSysBaseTypeList.get(0).getCodeName();
                        //手机卖场（全国连锁）和 手机卖场（非全国连锁）
                        if (channelNode.getNodeKind() == 6 && channelNode.getNodeType() == 3) {
                            channelStar = "4";
                        } else if (channelNode.getNodeKind() == 6 && channelNode.getNodeType() == 4) {
                            channelStar = "3";
                        }
                    }
                }
                List<ChannelEntityRelationInfo> channelEntityRelationInfos = channelEntityRelationInfoDao.getChannelEntityRelationInfo(channelNode.getNodeId(), ChannelConstants.RELATION_TYPE_CONTACTS);
                if ((!channelEntityRelationInfos.isEmpty()) && channelEntityRelationInfos.size() == 1) {
                    ChannelEntityRelationInfo channelEntityRelationInfo = channelEntityRelationInfos.get(0);
                    relationMobile = channelEntityRelationInfo.getRelationMobile() == null ? "" : channelEntityRelationInfo.getRelationMobile();
                    relationName = channelEntityRelationInfo.getRelationName() == null ? "" : channelEntityRelationInfo.getRelationName();
                }
                map.put("channelStar", channelStar);//渠道星级
                map.put("coordinateSystem", "1");//经纬度坐标系
                map.put("longitude", longitude); //经度
                map.put("latitude", latitude); //纬度
                map.put("relationName", relationName);
                map.put("relationMobile", relationMobile);
//                map.put("panChannelType", panChannelType);
//                map.put("panChannelExpansionMode", panChannelExpansionMode);
                map.put("regionId", regionId);
                map.put("regionName", regionName);
                List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.getParentEntity(nodeId);
                Long agentId = null;
                String legalRepresent = "";
                String idNumber = "";
                String channelCompanyName = "";
                String socIden = "";
                String idType = "";
                if (!channelEntityRelInfoList.isEmpty()) {
                    agentId = channelEntityRelInfoList.get(0).getParentEntity();
                    List<AgentAccountInfo> agentAccountInfos = agentAccountInfoDao.query(agentId);
                    if (!agentAccountInfos.isEmpty()) {
                        //法人代表+证件类型+证件号(自然人)
                        idType = "1";
                        idNumber = agentAccountInfos.get(0).getIdentifyCode() == null ? "" : agentAccountInfos.get(0).getIdentifyCode();
                        //自然人没有法人代表，将供应商名称作为法人代表
                        if (agentAccountInfos.get(0).getVendorName() != null) {
                            legalRepresent = agentAccountInfos.get(0).getVendorName();
                        }
                        if (agentAccountInfos.get(0).getVendorName() != null) {
                            channelCompanyName = agentAccountInfos.get(0).getVendorName();
                        }
                        if (agentAccountInfos.get(0).getSocIden() != null) {
                            socIden = agentAccountInfos.get(0).getSocIden();
                        }
                    }
                }
                channelSysBaseTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(50034, 109, null, null);
                String password = channelSysBaseTypeList.get(0).getCodeName();
                if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(idNumber)) {
                    logger.info("新加密方式idNumber:" + idNumber);
                    logger.info("密钥:" + password);
                    idNumber = AESUtils.encrypt(idNumber, password);//要求对证件号码加密
                }
//                map.put("idType", "" + idType);//[1:身份证、2:护照、3:军官证、4:驾驶证]
//                map.put("idNumber", "" + idNumber);
//                map.put("legalRepresent", "" + this.enCodeUtf8(legalRepresent));
//                map.put("socialCreditCode", "" + socIden);//统一信用代码
//                map.put("channelCompanyName", "" + this.enCodeUtf8(channelCompanyName));//渠道所属公司名称

                map.put("stateEncoding", stateEncoding);//状态编码

                map.put("geographicalPosition", geographicalPosition);//城乡级别
                //map.put("natureOfProperty", natureOfProperty);//物业类型----房产性质
                map.put("shopArea", shopArea);//店铺面积---总面积
                map.put("geographicalPositionType", geographicalPositionType);//地理位置
                map.put("rentAmount", rentAmount);//购置或租赁成本
//                map.put("subDivision", subDivision);//渠道细分形态
//                map.put("terminalSalesFormat", terminalSalesFormat);//终端销售业态
//                map.put("managementStyle", managementStyle);//管理方式
//                map.put("cooperationType", cooperationType);//合作形式
//                map.put("businessPatterns", businessPatterns);//业务形态
//                map.put("industryPatterns", industryPatterns);//行业形态
//                map.put("industryPatternsAttrs", industryPatternsAttrs);//行业形态（属性）
            } else {
                map.put("error", "未查到符合条件的网点数据信息");
            }
        } catch (Exception e) {
            logger.error("根据orgId查询对应接口失败:", e);
            map.put("error", "根据orgId查询对应接口失败,接口异常！！！");
            return map;
        }
        logger.info("查询申请全网统一编码所需信息，根据orgId查询对应接口结束");
        return map;
    }

    @Override
    public Map<String, Object> getAgentInfoByOrgId(String param) throws Exception {
        logger.info("根据CRM侧的orgId查询对应接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        String orgId = "";
        ChannelOrgAgent channelOrgAgent2 = null;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            orgId = jsonParam.getString("orgId");

        } catch (Exception e) {
            logger.error("获取入参参数：根据orgId查询对应PT-SH-FS-OI1209接口失败:", e);
            logger.debug("********    根据orgId查询对应接口  结束  ********");
            map.put("error", "入参格式错误");
            return map;
        }
        try {
            //1根据orgId查询实体对应关系
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setOrgId(Long.valueOf(orgId));
            List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgents.size() <= 0) {
                map.put("error", "根据orgId未查询到实体对应关系数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() > 1) {
                map.put("error", "根据orgId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() == 1) {
                channelOrgAgent2 = channelOrgAgents.get(0);
                Long nodeId = channelOrgAgent2.getAgentId();
                List<ChannelAgentInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.getChannelParentEntityInfo(nodeId);
                String channelEntityName = "";
                Integer agentFlag = 99;//默认为其他
                if (!CollectionUtils.isEmpty(channelEntityBasicInfos)) {
                    channelEntityName = channelEntityBasicInfos.get(0).getFullName();
                    map.put("agentName", "" + agentFlag);
                    map.put("agentNameSupplement", channelEntityName);
                    String panChannelType = "";
                    String panChannelExpansionMode = "";
                    List<ChannelNode> channelNodeList = channelNodeDao.getNodeInfo(nodeId);
                    if (channelNodeList.size() > 0) {
                        ChannelNode channelNode = channelNodeList.get(0);
                        panChannelType = channelNode.getPanChannelType() == null ? "" : channelNode.getPanChannelType().toString();
                        panChannelExpansionMode = channelNode.getPanChannelExpansionMode() == null ? "" : channelNode.getPanChannelExpansionMode().toString();
                    }
//                    map.put("panChannelType", panChannelType);
//                    map.put("panChannelExpansionMode", panChannelExpansionMode);
                } else {
                    map.put("error", "未查到符合条件的合作方数据数据信息");
                }
            }
        } catch (Exception e) {
            logger.error("根据orgId查询对应接口失败:", e);
            map.put("error", "根据orgId查询对应接口失败,接口异常！！！");
            return map;
        }
        logger.info("查询申请全网统一编码所需信息，根据orgId查询对应接口失败 结束");
        return map;
    }

    @Override
    public String getAgentInfoByOrgIdEsb(String param) throws Exception {
        Response response = new Response();
        logger.info("根据CRM侧的orgId查询对应接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        String result = "";
        String orgId = "";
        ChannelOrgAgent channelOrgAgent2 = null;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            orgId = jsonParam.getString("orgId");

        } catch (Exception e) {
            logger.error("获取入参参数：根据orgId查询对应PT-SH-ESB-CHANNEL-1217接口失败:", e);
            logger.debug("********    根据orgId查询对应接口  结束  ********");
            response.setCode(Response.ERROR);
            response.setMessage("入参格式错误！！！");
            return response.toString();
        }
        try {
            //根据orgId查询实体对应关系
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setOrgId(Long.valueOf(orgId));
            List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgents.size() <= 0) {
                response.setCode(Response.ERROR);
                response.setMessage("根据orgId未查询到实体对应关系数据，请查证,入参orgId：" + orgId);
            } else if (channelOrgAgents.size() > 1) {
                response.setCode(Response.ERROR);
                response.setMessage("根据orgId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
            } else if (channelOrgAgents.size() == 1) {
                channelOrgAgent2 = channelOrgAgents.get(0);
                Long nodeId = channelOrgAgent2.getAgentId();
                List<ChannelAgentInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.getChannelParentEntityInfo(nodeId);
                if (!CollectionUtils.isEmpty(channelEntityBasicInfos)) {
                    map.put("agentName", channelEntityBasicInfos.get(0).getFullName());
                    map.put("agentId", channelEntityBasicInfos.get(0).getAgentId());
                    result = JsonUtil.object2Json(map);//生成json串
                    response.setCode(Response.SUCCESS);
                    response.setData(result);
                } else {
                    response.setCode(Response.ERROR);
                    response.setMessage("未查到符合条件的合作方数据信息!!");
                }
            }
        } catch (Exception e) {
            logger.error("根据orgId查询对应接口失败:", e);
            response.setCode(Response.ERROR);
            response.setMessage("根据orgId查询对应接口失败,接口异常！！！");
            return response.toString();
        }
        return response.toString();
    }


    private void changeDateForm(JSONObject jsonOb, ChannelNodeDtl channelNodeDtl) {
        //时间类型转换
        if (jsonOb.containsKey("signBeginDate") && StringUtils.isNotBlank(jsonOb.getString("signBeginDate"))) {  // 签约时间
            channelNodeDtl.setSignBeginDate(new Date(Long.parseLong(jsonOb.getString("signBeginDate"))));
        }
        if (jsonOb.containsKey("signEndDate") && StringUtils.isNotBlank(jsonOb.getString("signEndDate"))) {    //截止时间
            channelNodeDtl.setSignEndDate(new Date(Long.parseLong(jsonOb.getString("signEndDate"))));
        }
        if (jsonOb.containsKey("businessStartDate") && StringUtils.isNotBlank(jsonOb.getString("businessStartDate"))) {    //开业时间
            channelNodeDtl.setBusinessStartDate(new Date(Long.parseLong(jsonOb.getString("businessStartDate"))));
        }
        if (jsonOb.containsKey("lastDecorateTime") && StringUtils.isNotBlank(jsonOb.getString("lastDecorateTime"))) {    //最近一次整体装修时间
            channelNodeDtl.setLastDecorateTime(new Date(Long.parseLong(jsonOb.getString("lastDecorateTime"))));
        }
        if (jsonOb.containsKey("purchaseDate") && StringUtils.isNotBlank(jsonOb.getString("purchaseDate"))) {    //购买时间
            channelNodeDtl.setPurchaseDate(new Date(Long.parseLong(jsonOb.getString("purchaseDate"))));
        }
        if (jsonOb.containsKey("rentStartDate") && StringUtils.isNotBlank(jsonOb.getString("rentStartDate"))) {    //租赁起始时间
            channelNodeDtl.setRentStartDate(new Date(Long.parseLong(jsonOb.getString("rentStartDate"))));
        }
        if (jsonOb.containsKey("rentEndDate") && StringUtils.isNotBlank(jsonOb.getString("rentEndDate"))) {    //租赁截止时间
            channelNodeDtl.setRentEndDate(new Date(Long.parseLong(jsonOb.getString("rentEndDate"))));
        }
    }

    private Map<String, Object> queryChannelNodeByChanneId(JSONObject jsonParam) {
        logger.info("新系统查询老系统渠道 接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        ChannelNodeDtl channelNodeDtl = null;
        try {
//            JSONObject jsonParam = JSONObject.fromObject(param);
            String channelNodeDtlStr = jsonParam.getString("channelNodeDtl");
            channelNodeDtl = (ChannelNodeDtl) JSONObject.toBean(JSONObject.fromObject(channelNodeDtlStr), ChannelNodeDtl.class);
//            channelNodeDtl = channelNodeService.getChannelNodeInfo(Long.valueOf(channelNodeDtl.getChannelEntityId()));
            // 获取网点信息
            List<ChannelNodeDtl> channelNodeDtlList = channelNodeDao.getChannelNodeInfo(Long.valueOf(channelNodeDtl.getChannelEntityId()));
            if (channelNodeDtlList == null || channelNodeDtlList.size() < 1) {
                map.put("error", "没有查询到，渠道ID：" + jsonParam.getString("channelId"));
                map.put("resultCode", "-1");
            }
        } catch (Exception e) {
            logger.error("新系统查询老系统渠道 接口失败:", e);
            map.put("error", e.getMessage());
            map.put("resultCode", "-1");
            return map;
        }
        logger.info("新系统查询老系统渠道网点 接口 结束");
        return map;
    }

    /**
     * 同步新系统修改网点信息到老系统
     *
     * @param jsonParam
     * @return
     */
    private Map<String, Object> updateChannelNodeInfo(JSONObject jsonParam) {
        logger.info("修改新系统网点 同步老系统 接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        SPrivData sPrivData = null;
        ChannelNodeDtl channelNodeDtl = null;
        try {
//            JSONObject jsonParam = JSONObject.fromObject(param);
            String sPrivDataStr = jsonParam.getString("sPrivData");
            String channelNodeDtlStr = jsonParam.getString("channelNodeDtl");
            sPrivData = (SPrivData) JSONObject.toBean(JSONObject.fromObject(sPrivDataStr), SPrivData.class);
            channelNodeDtl = (ChannelNodeDtl) JSONObject.toBean(JSONObject.fromObject(channelNodeDtlStr), ChannelNodeDtl.class);
            //时间类型转换
            changeDateForm(JSONObject.fromObject(channelNodeDtlStr), channelNodeDtl);
        } catch (Exception e) {
            logger.error("获取入参参数：修改新系统网点 同步老系统接口失败:", e);
            logger.debug("********    修改新系统网点 同步老系统 接口 结束  ********");
            map.put("error", "入参格式错误");
            map.put("resultCode", "-1");
            return map;
        }
        try {
            Integer iRet = channelNodeService.updChannelNodeInfo(sPrivData, channelNodeDtl);
            map.put("resultCode", iRet.toString());
        } catch (Exception e) {
            logger.error("修改新系统网点 同步老系统接口失败:", e);
            map.put("error", e.getMessage());
            map.put("resultCode", "-1");
            return map;
        }
        logger.info("修改新系统网点 同步老系统 接口 结束");
        return map;
    }

    private Map<String, Object> delChannelNode(JSONObject jsonParam) {
        logger.info("新系统删除网点信息同步到老系统 接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        SPrivData sPrivData = null;
        String exitReason = "";
        try {
//            JSONObject jsonParam = JSONObject.fromObject(param);
            String sPrivDataStr = jsonParam.getString("sPrivData");
            sPrivData = (SPrivData) JSONObject.toBean(JSONObject.fromObject(sPrivDataStr), SPrivData.class);
            String nodeId = jsonParam.getString("channelId");
            List<Long> nodeIds = new ArrayList<Long>();
            nodeIds.add(Long.valueOf(nodeId));
            if (jsonParam.containsKey("exitReason")) {
                exitReason = jsonParam.getString("exitReason");
            }
            Integer iRet = channelNodeService.exitChannelNodeInfoOrList(sPrivData, nodeIds, exitReason);
            map.put("resultCode", iRet.toString());   //正常的话 iRet=0
        } catch (Exception e) {
            logger.error("新系统删除网点信息同步到老系统 接口失败:", e);
            map.put("error", e.getMessage());
            map.put("resultCode", "-1");
            return map;
        }
        logger.info("新系统删除网点信息同步到老系统 接口 结束");
        return map;
    }

    /**
     * 同步渠道新系统添加代理商
     */
    private Map<String, Object> addAgentInfo(JSONObject jsonParam) {
        logger.info("同步渠道新系统 添加代理商 接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        SPrivData sPrivData = null;
        ChannelAgentDtl channelAgentDtl = null;
        try {
//            JSONObject jsonParam = JSONObject.fromObject(param);
            String sPrivDataStr = jsonParam.getString("sPrivData");
            String channelAgentDtlStr = jsonParam.getString("channelAgentDtl");
            sPrivData = (SPrivData) JSONObject.toBean(JSONObject.fromObject(sPrivDataStr), SPrivData.class);
            JSONObject jsonOb = JSONObject.fromObject(channelAgentDtlStr);
            channelAgentDtl = (ChannelAgentDtl) JSONObject.toBean(jsonOb, ChannelAgentDtl.class);
            //时间类型转换
            if (jsonOb.containsKey("signBeginDate") && StringUtils.isNotBlank(jsonOb.getString("signBeginDate"))) {  // 签约时间
                channelAgentDtl.setSignBeginDate(new Date(Long.parseLong(jsonOb.getString("signBeginDate"))));
            }
            if (jsonOb.containsKey("signEndDate") && StringUtils.isNotBlank(jsonOb.getString("signEndDate"))) {    //截止时间
                channelAgentDtl.setSignEndDate(new Date(Long.parseLong(jsonOb.getString("signEndDate"))));
            }
        } catch (Exception e) {
            logger.error("获取入参参数：同步渠道新系统 添加代理商接口失败:", e);
            logger.debug("********    同步渠道新系统 添加代理商 接口 结束  ********");
            map.put("error", "入参格式错误");
            map.put("resultCode", "-1");
            return map;
        }
        try {
//            Integer iRet = channelNodeService.addChannelNodeInfo(sPrivData, channelNodeDtl);
//            ChannelAgentDtl channelAgentDtl,Long lChannelEntityId,Integer channelEntityStatus, Integer operTypeCode
            Integer iRet = channelAgentService.addChannelAgentInfoByNewChannel(sPrivData, channelAgentDtl, channelAgentDtl.getChannelEntityId(), ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT, ChannelConstants.AICHNL_ARCHIVE_CHANNEL_APPLY_CHECK);

            map.put("resultCode", iRet.toString());
        } catch (Exception e) {
            logger.error("同步渠道新系统 添加代理商接口失败:", e);
            map.put("error", e.getMessage());
            map.put("resultCode", "-1");
            return map;
        }
        logger.info("同步渠道新系统 添加代理商 接口 结束");
        return map;
    }

    /**
     * 新系统调用该接口查询代理商信息
     *
     * @param jsonParam
     * @return
     */
    private Map<String, Object> queryAgentinfo(JSONObject jsonParam) {
//        getChannelAgentList
        logger.info("新系统 查询代理商 接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        SPrivData sPrivData = null;
        ChannelAgentDtl channelAgentDtl = null;
        try {
//            JSONObject jsonParam = JSONObject.fromObject(param);
            String sPrivDataStr = jsonParam.getString("sPrivData");
            String channelAgentDtlStr = jsonParam.getString("channelAgentDtl");
            sPrivData = (SPrivData) JSONObject.toBean(JSONObject.fromObject(sPrivDataStr), SPrivData.class);
            channelAgentDtl = (ChannelAgentDtl) JSONObject.toBean(JSONObject.fromObject(channelAgentDtlStr), ChannelAgentDtl.class);
        } catch (Exception e) {
            logger.error("获取入参参数：新系统 查询代理商接口失败:", e);
            logger.debug("********    新系统 查询代理商 接口 结束  ********");
            map.put("error", "入参格式错误");
            map.put("resultCode", "-1");
            return map;
        }
        try {
            PageData<ChannelAgentDtl> channelAgentDtlPageData = channelAgentService.getChannelAgentList(channelAgentDtl.getChannelEntityId(), null, null, null, channelAgentDtl.getChannelEntityStatuss(), new PageParameter());
            if (channelAgentDtlPageData.getRows() != null && channelAgentDtlPageData.getRows().size() > 0) {
                map.put("resultCode", ChannelConstants.RETURN_SUCCESS);
            }
        } catch (Exception e) {
            logger.error("新系统 查询代理商接口失败:", e);
            map.put("error", e.getMessage());
            map.put("resultCode", "-1");
            return map;
        }
        logger.info("新系统 查询代理商 接口 结束");
        return map;
    }

    /**
     * 新系统调用该接口修改代理商信息
     *
     * @param jsonParam
     * @return
     */
    private Map<String, Object> updAgentinfo(JSONObject jsonParam) {
//        getChannelAgentList
        logger.info("新系统 修改代理商 接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        SPrivData sPrivData = null;
        ChannelAgentDtl channelAgentDtl = null;
        try {
//            JSONObject jsonParam = JSONObject.fromObject(param);
            String sPrivDataStr = jsonParam.getString("sPrivData");
            String channelAgentDtlStr = jsonParam.getString("channelAgentDtl");
            sPrivData = (SPrivData) JSONObject.toBean(JSONObject.fromObject(sPrivDataStr), SPrivData.class);
            channelAgentDtl = (ChannelAgentDtl) JSONObject.toBean(JSONObject.fromObject(channelAgentDtlStr), ChannelAgentDtl.class);
            //时间类型转换
            if (jsonParam.containsKey("signBeginDate") && StringUtils.isNotBlank(jsonParam.getString("signBeginDate"))) {  // 签约时间
                channelAgentDtl.setSignBeginDate(new Date(Long.parseLong(jsonParam.getString("signBeginDate"))));
            }
            if (jsonParam.containsKey("signEndDate") && StringUtils.isNotBlank(jsonParam.getString("signEndDate"))) {    //截止时间
                channelAgentDtl.setSignEndDate(new Date(Long.parseLong(jsonParam.getString("signEndDate"))));
            }
        } catch (Exception e) {
            logger.error("获取入参参数：新系统 修改代理商接口失败:", e);
            map.put("error", "入参格式错误");
            map.put("resultCode", "-1");
            return map;
        }
        try {
            Integer iRet = channelAgentService.updChannelAgentInfo(sPrivData, channelAgentDtl);
//            (sPrivData, channelAgentDtl, channelAgentDtl.getChannelEntityId(), ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT,
//                              ChannelConstants.CHANNEL_CHECK_RESULT_PASS);
            map.put("resultCode", iRet.toString());
        } catch (Exception e) {
            logger.error("新系统 修改代理商接口失败:", e);
            map.put("error", e.getMessage());
            map.put("resultCode", "-1");
            return map;
        }
        logger.info("新系统 修改代理商 接口 结束");
        return map;
    }

    private Map<String, Object> delAgentinfo(JSONObject jsonParam, boolean isPreAway) {
        logger.info("新系统 删除代理商 接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        SPrivData sPrivData = null;
        String exitReason = "";
        try {
//            JSONObject jsonParam = JSONObject.fromObject(param);
            String sPrivDataStr = jsonParam.getString("sPrivData");
            sPrivData = (SPrivData) JSONObject.toBean(JSONObject.fromObject(sPrivDataStr), SPrivData.class);
            String agentId = jsonParam.getString("channelId");
            Integer iRet = -1;
//            List<Long> nodeIds = new ArrayList<Long>();
//            nodeIds.add(Long.valueOf(nodeId));
            if (jsonParam.containsKey("exitReason")) {
                exitReason = jsonParam.getString("exitReason");
            }
//            Integer iRet = channelNodeService.exitChannelNodeInfoOrList(sPrivData, nodeIds, exitReason);
            if (isPreAway) {
                iRet = channelAgentService.preAwayChannelAgentInfo(sPrivData, Long.valueOf(agentId));
            }
            if (!isPreAway) {
                iRet = channelAgentService.awayChannelAgentInfo(sPrivData, Long.valueOf(agentId), exitReason);
            }
            map.put("resultCode", iRet.toString());   //正常的话 iRet=0
        } catch (Exception e) {
            logger.error("新系统删除网点信息同步到老系统 接口失败:", e);
            map.put("error", e.getMessage());
            map.put("resultCode", "-1");
            return map;
        }

        logger.info("新系统 删除代理商 接口 结束");
        return map;
    }

    private Map<String, Object> getAgentSerial(JSONObject jsonParam) {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String strAgentShortName = jsonParam.getString("channelEntityName");
        String agentLevel = jsonParam.getString("agentLevel");
        StringBuilder strAgentSerial = new StringBuilder();
        Integer iSubCodeLen;
        Integer iSubCodeNum;
        try {
            //首先添加代理商拼音简称
            //String agentFirstOrFullSpell = channelEntityBasicInfoDao.getFirstOrFullSpell(strAgentShortName);
            String agentFirstOrFullSpell = PinyinUtils.ToFirstChar(strAgentShortName);
            if (agentFirstOrFullSpell.length() == 0) {
                throw new Exception("获取代理商的简称拼音信息失败！");
            }
            strAgentSerial.append(agentFirstOrFullSpell);
            //查询补位表进行计算
            AgentSerialConfig agentSerialConfig4Query = new AgentSerialConfig();
            agentSerialConfig4Query.setShortNamePy(agentFirstOrFullSpell);
            List<AgentSerialConfig> listAgentSerialConfig = agentSerialConfigDao.query(agentSerialConfig4Query);
            AgentSerialConfig agentSerialConfig;
            //首次出现加入配置表
            if (listAgentSerialConfig.size() == 0) {
                agentSerialConfig = new AgentSerialConfig();
                agentSerialConfig.setShortNamePy(agentFirstOrFullSpell);
                agentSerialConfig.setNamePosition(0);
                iSubCodeLen = ChannelConstants.MAX_AGENT_SERIAL - strAgentSerial.length();
                if (iSubCodeLen < 1) {
                    throw new Exception("代理商简称超过4位,生成代理商编码失败！");
                }
                agentSerialConfig.setAddLength(iSubCodeLen);
                agentSerialConfigDao.insert(agentSerialConfig);
                iSubCodeNum = 0;
            } else {
                agentSerialConfig = listAgentSerialConfig.get(0);
                if (agentSerialConfig.getNamePosition() == ChannelConstants.MAX_NAME_POSITION) {
                    throw new Exception("该代理商简称拼音首字母组合为 " + agentSerialConfig.getShortNamePy() + " ，该组合超过35个，生成代理商编码失败，请更改代理商简称。");
                }
                iSubCodeNum = agentSerialConfig.getNamePosition() + 1;
                agentSerialConfig.setNamePosition(iSubCodeNum);
                iSubCodeLen = ChannelConstants.MAX_AGENT_SERIAL - strAgentSerial.length();
                agentSerialConfigDao.update(agentSerialConfig);
            }
            //获取代理商补码
            StringBuilder agentSubCode = new StringBuilder();
            for (int i = 1; i < iSubCodeLen; i++) {
                agentSubCode.append("0");
            }
            agentSubCode.append(ChannelConstants.CHANNEL_ENTITY_DEC_2_X.charAt(iSubCodeNum % ChannelConstants.MAX_NAME_POSITION_COUNT));
//        strAgentSerial.append(get_decToX(iSubCodeNum, iSubCodeLen));
            strAgentSerial.append(agentSubCode.toString());


            //获取代理商编码的末位,新系统没有代理商类型，所以统一按照非连锁商的格式处理
            ChannelSysBaseType channelSysBaseType4Query = new ChannelSysBaseType();
            channelSysBaseType4Query.setCodeType(10004);
            channelSysBaseType4Query.setTag(1);
            channelSysBaseType4Query.setCodeId(Integer.valueOf(agentLevel));
            List<ChannelSysBaseType> listChannelSysBaseType = channelSysBaseTypeDao.query(channelSysBaseType4Query);
            for (ChannelSysBaseType channelSysBaseType : listChannelSysBaseType) {
                //	strAgentSerial.append(channelSysBaseType.getExt1());
            }
        } catch (Exception e) {
            logger.error("获取代理商编码失败", e);
//            throw new Exception("全网统一编码获取失败"+e.getMessage());
            retMap.put("resultCode", "-1");
            return retMap;
        }
        //因为没有类型，所以，为了代理商 的六位数，所以，统一最后一位设置为0
        strAgentSerial.append("0");
        retMap.put("agentSerial", strAgentSerial.toString());
        retMap.put("resultCode", "1");
        return retMap;
    }

    /**
     * 得到渠道编码
     *
     * @param jsonParam
     * @return
     */
    private Map<String, Object> getUnifyCode(JSONObject jsonParam) {
        Map<String, Object> retMap = new HashMap<String, Object>();
//        JSONObject jsonParam = JSONObject.fromObject(param);
        String sPrivDataStr = jsonParam.getString("sPrivData");
        SPrivData sPrivData = (SPrivData) JSONObject.toBean(JSONObject.fromObject(sPrivDataStr), SPrivData.class);
        String channelEntityId = jsonParam.getString("channelEntityId");
        String parentEntity = jsonParam.getString("parentEntity");
        String channelEntityName = jsonParam.getString("channelEntityName");
        String agentName = jsonParam.getString("parentEntityName");
        String nodeAdress = jsonParam.getString("nodeAddr");
        String regionId = jsonParam.getString("regionId");
        String districtId = jsonParam.getString("districtId");
        String operate = jsonParam.getString("operate");
        Map<String, Object> busiParams = new HashMap<String, Object>();   //封装业务参数

        if (operate == null) {
            logger.error("操作类型为空，操作全网统一编码失败。");
            return retMap;
        }

        if ("add".equals(operate)) {
            busiParams.put("iOperateType", ChannelConstants.PREORDER_ADD);
        }
        if ("del".equals(operate)) {
            busiParams.put("iOperateType", ChannelConstants.PREORDER_DEL);
        }

        busiParams.put("m_strCodeId", "");
        busiParams.put("m_llOrgId", channelEntityId);

        String netType = "2";
        Long parentEntityId = Long.valueOf(parentEntity);
        if (null == parentEntityId) {
            logger.error("网点[" + channelEntityName + "]获取父代理商信息失败。");
            return retMap;
        }

        //获取全网接入代理商信息
        ChannelSysBaseType channelSysBaseType = new ChannelSysBaseType();
        channelSysBaseType.setCodeType(900005);
        channelSysBaseType.setCodeId(parentEntityId.intValue());
        List<ChannelSysBaseType> channelSysBaseTypeList = channelSysBaseTypeDao.query(channelSysBaseType);
        if (channelSysBaseTypeList == null) {
            logger.error("网点[" + channelEntityName + "]获取全网接入代理商信息失败。");
        }
        if (channelSysBaseTypeList.size() == 0) {
            netType = "2";
        }
        if (channelSysBaseTypeList.size() > 0) {
            netType = "1";
        }
        busiParams.put("m_nNetType", netType);
        busiParams.put("m_nProvCode", 0);

        String strExternCode = "138";
        List<ChannelSysBaseType> sysBaseTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10023, Integer.valueOf(regionId), Integer.valueOf(districtId), null);
        if (sysBaseTypeList.size() == 1) {
            strExternCode = sysBaseTypeList.get(0).getExternCode();
        }
        busiParams.put("m_nCityCode", strExternCode); //城市编码
        busiParams.put("m_nTownCode", 0);
        busiParams.put("m_nChannelStyle", 1);
        busiParams.put("m_nChannelBody", 0);
        busiParams.put("m_nChannelForm", 0);
        busiParams.put("m_nChannelMarks", 999); //渠道特征编码
        busiParams.put("m_nChannelPosition", 0);
        busiParams.put("m_nChannelOrgan", 0);
        busiParams.put("m_nChannelStatus", 0);
        busiParams.put("m_nStatus", 0);
        busiParams.put("m_strDoneDate", "");
        busiParams.put("m_strExt1", channelEntityName);
        busiParams.put("m_llExt2", 0);
        busiParams.put("m_strExt3", "");
        busiParams.put("m_llExt4", 0);
        busiParams.put("m_strExt5", "");
        busiParams.put("m_llExt6", 0);
        busiParams.put("m_nShopAddr", nodeAdress);
        busiParams.put("m_nChannelCompany", agentName);
        busiParams.put("m_strUnifiedCodeId", "");
        String strUnifyCode = null;

        try {
            if ("add".equals(operate)) {
				/*String jsonString = channelSoaService.call("commit_codeOrgRel", busiParams, sPrivData); //调用
				JSONObject jb = JSONObject.fromObject(jsonString);
				strUnifyCode = jb.get("llUnifiedCodeId").toString();
				retMap.put("m_strUnifiedCodeId",strUnifyCode);*/
                retMap.put("resultCode", "1");
            }
            if ("del".equals(operate)) {
                //channelSoaService.call("commit_codeOrgRel", busiParams, sPrivData); //调用
                retMap.put("resultCode", "1");
            }
        } catch (Exception e) {
            logger.error("全网统一编码获取失败", e);
//            throw new Exception("全网统一编码获取失败"+e.getMessage());
            retMap.put("resultCode", "-1");
        }
        return retMap;
    }

    /**
     * 获取CRM侧全网统一编码
     *
     * @param nodeId
     * @param orgId
     * @return
     */
    private String queryCRMUnifyCode(Long nodeId, Long orgId) throws Exception {
        String unifyCode = "";
        String orgIdStr = "";
        String nodeIdStr = "";
        try {
            if (null == nodeId && null == orgId) {
                logger.error("nodeId 或者 orgId为空！");
                throw new Exception("nodeId 或者 orgId为空！");
            }
            //封装业务参数
            Map<String, Object> operatorMap = new HashMap<String, Object>();
            SPrivData sPrivData = new SPrivData();
            sPrivData.setOpId(999990131L);
            sPrivData.setOrgId(1L);
            if (nodeId == null) {
                operatorMap.put("node_id", "" + nodeIdStr);
            } else {
                operatorMap.put("node_id", "" + nodeId);
            }

            if (orgId == null) {
                operatorMap.put("org_id", orgIdStr);
            } else {
                operatorMap.put("org_id", "" + orgId);
            }
            String jsonStr = channelSoaService.call2("getQueryCRMUnifyCode", operatorMap, sPrivData);
            JSONObject js = JSONObject.fromObject(jsonStr);
            if (jsonStr.equals("null")) {
                logger.error("获取CRM侧全网统一编码，返回为空");
            }
            String jsonStrError = js.getJSONObject("Response").getJSONObject("ErrorInfo").get("Code").toString();
            /***
             * 用上可以在自己调用处加上判断
             */
            if ("0010".equals(jsonStrError)) {
                logger.error("获取CRM侧全网统一编码，业务方法执行超时");
            } else if ("0006".equals(jsonStrError)) {
                logger.error("获取CRM侧全网统一编码，业务未定义");
            } else if ("1043".equals(jsonStrError)) {
                logger.error("获取CRM侧全网统一编码，org_id为空");
            } else {
                String jsonString = js.getJSONObject("Response").getJSONObject("RetInfo").toString();
                JSONObject jsonObject = JSONObject.fromObject(jsonString);
                if (jsonObject.size() > 0 && jsonObject.getString("unifiedCodeId").equals("")) {
                    logger.info("返回全网统一编码为空");
                } else if (jsonObject.size() > 0 && !(jsonObject.getString("unifiedCodeId").equals(""))) {
                    unifyCode = jsonObject.getString("unifiedCodeId");
                } else if (jsonObject.size() == 0) {
                    logger.info("获取CRM全网统一编码时接口出错");
                }
            }
        } catch (Exception e) {
            logger.error("获取CRM侧全网统一编码 ，调用外部接口失败", e);
            return unifyCode;
        }
        return unifyCode;
    }

    /**
     * 将库中的营业时间进行格式转换
     */
    public List<String> convertTime(String businessTime) {
        String monday = "";
        String tuesday = "";
        String wednesday = "";
        String thursday = "";
        String friday = "";
        String saturday = "";
        String sunday = "";
        String[] businessTimes = businessTime.split("\\|");
        if ("1".equals(businessTimes[0])) {
            for (int i = 1; i < businessTimes.length; i++) {
                String[] time = businessTimes[i].split("\\;");
                if ("1".equals(time[0])) {
                    monday = time[1];
                } else if ("2".equals(time[0])) {
                    tuesday = time[1];
                } else if ("3".equals(time[0])) {
                    wednesday = time[1];
                } else if ("4".equals(time[0])) {
                    thursday = time[1];
                } else if ("5".equals(time[0])) {
                    friday = time[1];
                } else if ("6".equals(time[0])) {
                    saturday = time[1];
                } else if ("0".equals(time[0])) {
                    sunday = time[1];
                }
            }
        } else if ("0".equals(businessTimes[0])) {
            String[] business = businessTimes[1].split("\\,");
            for (int i = 0; i < business.length; i++) {
                if ("1".equals(business[i])) {
                    monday = businessTimes[2];
                } else if ("2".equals(business[i])) {
                    tuesday = businessTimes[2];
                } else if ("3".equals(business[i])) {
                    wednesday = businessTimes[2];
                } else if ("4".equals(business[i])) {
                    thursday = businessTimes[2];
                } else if ("5".equals(business[i])) {
                    friday = businessTimes[2];
                } else if ("6".equals(business[i])) {
                    saturday = businessTimes[2];
                } else if ("0".equals(business[i])) {
                    sunday = businessTimes[2];
                }
            }
        }
        List<String> list = new ArrayList<String>();
        list.add(sunday);
        list.add(monday);//周一营业时间
        list.add(tuesday);
        list.add(wednesday);
        list.add(thursday);
        list.add(friday);
        list.add(saturday);
        return list;
    }


    public void updateShidianInfo(Long channelEntityId, String channelEntityName, String monday, String tuesday, String wednesday, String thursday, String friday,
                                  String saturday, String sunday, String unifyCode, String reginName, String reginCode, String longitude, String latitude,
                                  String nodeAddr, String relationName, String relationMobile, String email, String serviceIds, String syncDate,
                                  String serviceNames) throws Exception {
        ChannelShidianInfo channelShidianInfo = new ChannelShidianInfo();
        channelShidianInfo.setChannelEntityId(channelEntityId);
        channelShidianInfo.setChannelEntityName(channelEntityName);
        channelShidianInfo.setMonday(monday);
        channelShidianInfo.setTuesday(tuesday);
        channelShidianInfo.setWednesday(wednesday);
        channelShidianInfo.setThursday(thursday);
        channelShidianInfo.setFriday(friday);
        channelShidianInfo.setSaturday(saturday);
        channelShidianInfo.setSunday(sunday);
        channelShidianInfo.setUnifyCode(unifyCode);
        channelShidianInfo.setReginName(reginName);
        channelShidianInfo.setReginCode(reginCode);
        channelShidianInfo.setLongitude(longitude);
        channelShidianInfo.setLatitude(latitude);
        channelShidianInfo.setNodeAddr(nodeAddr);
        channelShidianInfo.setRelationName(relationName);
        channelShidianInfo.setRelationMobile(relationMobile);
        channelShidianInfo.setEmail(email);
        channelShidianInfo.setServiceId(serviceIds);
        channelShidianInfo.setServiceName(serviceNames);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date doneDate = simpleDateFormat.parse(syncDate);
        channelShidianInfo.setDoneDate(doneDate);
        channelShidianInfoDao.update(channelShidianInfo);

    }

    /**
     * 将查询出的营业厅的信息和配置在试点营业厅信息表中的信息进行比对
     */
    public List<ChannelShidianInfo> checkIsUpdate(Long channelEntityId, String monday, String tuesday, String wednesday, String thursday, String friday,
                                                  String saturday, String sunday, String reginName, String reginCode, String longitude, String latitude,
                                                  String nodeAddr, String relationName, String relationMobile, String email, String serviceIds,
                                                  String serviceNames, Integer label) {
        List<ChannelShidianInfo> changeList = new ArrayList<ChannelShidianInfo>();
        List<ChannelShidianInfo> channelShidianInfoList = new ArrayList<ChannelShidianInfo>();
        ChannelShidianInfo channelShidianInfo = new ChannelShidianInfo();
        ChannelShidianInfo channelShidianInfo1 = new ChannelShidianInfo();
        channelShidianInfo1.setChannelEntityId(channelEntityId);
        channelShidianInfoList = channelShidianInfoDao.query(channelShidianInfo1);
        channelShidianInfo = channelShidianInfoList.get(0);
        if (!((channelShidianInfo.getMonday() == null && "".equals(monday)) || (monday.equals(channelShidianInfo.getMonday())))) {
            channelShidianInfo1.setMonday(monday);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getTuesday() == null && "".equals(tuesday)) || (tuesday.equals(channelShidianInfo.getTuesday())))) {
            channelShidianInfo1.setTuesday(tuesday);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (label != null && label != channelShidianInfo.getLabel()) {
            channelShidianInfo1.setLabel(label);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getWednesday() == null && "".equals(wednesday)) || (wednesday.equals(channelShidianInfo.getWednesday())))) {
            channelShidianInfo1.setWednesday(wednesday);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getThursday() == null && "".equals(thursday)) || (thursday.equals(channelShidianInfo.getThursday())))) {
            channelShidianInfo1.setThursday(thursday);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getFriday() == null && "".equals(friday)) || (friday.equals(channelShidianInfo.getFriday())))) {
            channelShidianInfo1.setFriday(friday);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getSaturday() == null && "".equals(saturday)) || (saturday.equals(channelShidianInfo.getSaturday())))) {
            channelShidianInfo1.setSaturday(saturday);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getSunday() == null && "".equals(sunday)) || (sunday.equals(channelShidianInfo.getSunday())))) {
            channelShidianInfo1.setSunday(sunday);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getReginName() == null && "".equals(reginName)) || (reginName.equals(channelShidianInfo.getReginName())))) {
            channelShidianInfo1.setReginName(reginName);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getReginCode() == null && "".equals(reginCode)) || (reginCode.equals(channelShidianInfo.getReginCode())))) {
            channelShidianInfo1.setReginCode(reginCode);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getLatitude() == null && "".equals(latitude)) || (latitude.equals(channelShidianInfo.getLatitude())))) {
            channelShidianInfo1.setLatitude(latitude);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getLongitude() == null && "".equals(longitude)) || (longitude.equals(channelShidianInfo.getLongitude())))) {
            channelShidianInfo1.setLongitude(longitude);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getNodeAddr() == null && "".equals(nodeAddr)) || (nodeAddr.equals(channelShidianInfo.getNodeAddr())))) {
            channelShidianInfo1.setNodeAddr(nodeAddr);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getRelationName() == null && "".equals(relationName)) || (relationName.equals(channelShidianInfo.getRelationName())))) {
            channelShidianInfo1.setRelationName(relationName);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getRelationMobile() == null && "".equals(relationMobile)) || (relationMobile.equals(channelShidianInfo.getRelationMobile())))) {
            channelShidianInfo1.setRelationMobile(relationMobile);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getEmail() == null && "".equals(email)) || (email.equals(channelShidianInfo.getEmail())))) {
            channelShidianInfo1.setEmail(email);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getServiceId() == null && "".equals(serviceIds)) || (serviceIds.equals(channelShidianInfo.getServiceId())))) {
            channelShidianInfo1.setServiceId(serviceIds);
            channelShidianInfo1.setDoneDate(new Date());
        }
        if (!((channelShidianInfo.getServiceName() == null && "".equals(serviceNames)) || (serviceNames.equals(channelShidianInfo.getServiceName())))) {
            channelShidianInfo1.setServiceName(serviceNames);
            channelShidianInfo1.setDoneDate(new Date());
        }
        changeList.add(channelShidianInfo1);
        return changeList;
    }

    public List<JSONObject> insertReturnMsg(String channelEntityName, String monday, String tuesday, String wednesday, String thursday, String friday,
                                            String saturday, String sunday, String unifyCode, String reginName, String reginCode, String longitude, String latitude,
                                            String nodeAddr, String relationName, String relationMobile, String email, String flag,
                                            String serviceIds, String serviceNames, Integer label) {

        String serviceDetail = "无";
        String hallProvince = "上海省";
        String hallProvinceCode = "210";
        String hallCityCode = "021";
        String route = "暂无";
        String serviceId = "";
        String serviceName = "";
        List<JSONObject> dataList1 = new ArrayList<JSONObject>();
        Map<String, Object> hallInfoList = new HashMap<String, Object>();
        Map<String, String> KallOpenTimeList1 = new HashMap<String, String>();
        Map<String, String> serviceList1 = new HashMap<String, String>();
        JSONArray serviceList = new JSONArray();
        String[] serviceId1 = serviceIds.split("\\,");
        String[] serviceName1 = serviceNames.split("\\,");
        for (int i = 0; i < serviceId1.length; i++) {
            serviceId = serviceId1[i];
            serviceName = serviceName1[i];
            serviceList1.put("serviceID", serviceId);//业务大类ID
            serviceList1.put("serviceName", serviceName);//业务类别名称
            serviceList1.put("serviceDetail", serviceDetail);//业务办理相关提示信息(可选);
            serviceList.add(serviceList1);
        }
        if (!"".equals(monday)) {
            KallOpenTimeList1.put("monday", monday);
        }//周一营业时间
        if (!"".equals(tuesday)) {
            KallOpenTimeList1.put("tuesday", tuesday);
        }
        if (!"".equals(wednesday)) {
            KallOpenTimeList1.put("wednesday", wednesday);
        }
        if (!"".equals(thursday)) {
            KallOpenTimeList1.put("thursday", thursday);
        }
        if (!"".equals(friday)) {
            KallOpenTimeList1.put("friday", friday);
        }
        if (!"".equals(saturday)) {
            KallOpenTimeList1.put("saturday", saturday);
        }
        if (!"".equals(sunday)) {
            KallOpenTimeList1.put("sunday", sunday);
        }
        JSONArray HallOpenTimeList = JSONArray.fromObject(KallOpenTimeList1);

        hallInfoList.put("serviceList", serviceList);
        hallInfoList.put("hallOpenTimeList", HallOpenTimeList);//营业厅营业时间
        hallInfoList.put("flag", flag);//标识
        if (!"".equals(unifyCode)) {
            hallInfoList.put("hallID", unifyCode);//营业厅编码
        }
        if (!"".equals(hallProvinceCode)) {
            hallInfoList.put("hallProvinceCode", hallProvinceCode);
        }
        if (!"".equals(hallProvince)) {
            hallInfoList.put("hallProvince", hallProvince);//省份
        }
        if (!"".equals(reginName)) {
            hallInfoList.put("hallCityName", reginName);//城市
        }
        if (!"".equals(hallCityCode)) {
            hallInfoList.put("hallCityCode", hallCityCode);//城市编码
        }
        if (!"".equals(reginCode)) {
            hallInfoList.put("hallAeraCode", reginCode);//区编码
        }
        if (!"".equals(channelEntityName)) {
            hallInfoList.put("hallName", channelEntityName);//营业厅名称
        }
        if (!"".equals(longitude)) {
            longitude = DDtoDMS(longitude, "E");
            hallInfoList.put("hallLon", longitude);//营业厅经度（可选）
        }
        if (!"".equals(latitude)) {
            latitude = DDtoDMS(latitude, "N");
            hallInfoList.put("hallLat", latitude);//营业厅纬度（可选）
        }
        if (!"".equals(nodeAddr)) {
            hallInfoList.put("hallLocation", nodeAddr);//营业厅地址
        }
        if (!"".equals(email)) {
            hallInfoList.put("hallContactorEmail", email);//联系人邮箱（可选）
        }
        if (!"".equals(relationName)) {
            hallInfoList.put("hallContactorName", relationName);//营业厅联系人
        }
        if (!"".equals(relationMobile)) {
            hallInfoList.put("hallContactorTel", relationMobile);//联系人电话（可选）
        }
        hallInfoList.put("route", route);//公交线路
        hallInfoList.put("bizType", serviceNames);//受理范围(可选)
        hallInfoList.put("label", label);
        JSONObject hallInfoList1 = JSONObject.fromObject(hallInfoList);
        dataList1.add(hallInfoList1);

        return dataList1;
    }

    public List<JSONObject> convertChangeList(ChannelShidianInfo channelShidianInfo1, String flag, String syncDate) throws Exception {
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        String longitude = "";
        String latitude = "";
        String nodeAddr = "";
        String relationName = "";
        String relationMobile = "";
        String email = "";
        String unifyCode = "";
        String reginCode = "";
        String reginName = "";
        String serviceNames = "";
        String serviceIds = "";
        String monday = "";
        String tuesday = "";
        String wednesday = "";
        String sunday = "";
        String thursday = "";
        String friday = "";
        String saturday = "";
        Integer label = 0;
        String serviceDetail = "无";
        String hallProvince = "上海省";
        String hallProvinceCode = "210";
        String hallCityCode = "021";
        String route = "暂无";
        Long channelEntityId = channelShidianInfo1.getChannelEntityId();
        String channelEntityName = channelShidianInfo1.getChannelEntityName();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date doneDate = simpleDateFormat.parse(syncDate);
        ChannelShidianInfo channelShidianInfo = new ChannelShidianInfo();
        channelShidianInfo1.setChannelEntityId(channelEntityId);
        if (channelShidianInfo1.getLongitude() != null) {
            longitude = channelShidianInfo1.getLongitude();
            channelShidianInfo.setLongitude(longitude);
        }
        if (channelShidianInfo1.getLatitude() != null) {
            latitude = channelShidianInfo1.getLatitude();
            channelShidianInfo.setLatitude(latitude);
        }
        if (channelShidianInfo1.getNodeAddr() != null) {
            nodeAddr = channelShidianInfo1.getNodeAddr();
            channelShidianInfo.setNodeAddr(nodeAddr);
        }
        if (channelShidianInfo1.getRelationName() != null) {
            relationName = channelShidianInfo1.getRelationName();
            channelShidianInfo.setRelationName(relationName);
        }
        if (channelShidianInfo1.getLabel() != null) {
            label = channelShidianInfo1.getLabel();
            channelShidianInfo.setLabel(label);
        }
        if (channelShidianInfo1.getRelationMobile() != null) {
            relationMobile = channelShidianInfo1.getRelationMobile();
            channelShidianInfo.setRelationMobile(relationMobile);
        }
        if (channelShidianInfo1.getEmail() != null) {
            email = channelShidianInfo1.getEmail();
            channelShidianInfo.setEmail(email);
        }
        if (channelShidianInfo1.getUnifyCode() != null) {
            unifyCode = channelShidianInfo1.getUnifyCode();
            channelShidianInfo.setUnifyCode(unifyCode);
        }
        if (channelShidianInfo1.getReginCode() != null) {
            reginCode = channelShidianInfo1.getReginCode();
            channelShidianInfo.setReginCode(reginCode);
        }
        if (channelShidianInfo1.getReginName() != null) {
            reginName = channelShidianInfo1.getReginName();
            channelShidianInfo.setReginName(reginName);
        }
        if (channelShidianInfo1.getServiceName() != null) {
            serviceNames = channelShidianInfo1.getServiceName();
            channelShidianInfo.setServiceName(serviceNames);
        }
        if (channelShidianInfo1.getServiceId() != null) {
            serviceIds = channelShidianInfo1.getServiceId();
            channelShidianInfo.setServiceId(serviceIds);
        }
        if (channelShidianInfo1.getMonday() != null) {
            monday = channelShidianInfo1.getMonday();
            channelShidianInfo.setMonday(monday);
        }
        if (channelShidianInfo1.getTuesday() != null) {
            tuesday = channelShidianInfo1.getTuesday();
            channelShidianInfo.setTuesday(tuesday);
        }
        if (channelShidianInfo1.getWednesday() != null) {
            wednesday = channelShidianInfo1.getWednesday();
            channelShidianInfo.setWednesday(wednesday);
        }
        if (channelShidianInfo1.getThursday() != null) {
            thursday = channelShidianInfo1.getThursday();
            channelShidianInfo.setThursday(thursday);
        }
        if (channelShidianInfo1.getFriday() != null) {
            friday = channelShidianInfo1.getFriday();
            channelShidianInfo.setFriday(friday);
        }
        if (channelShidianInfo1.getSaturday() != null) {
            saturday = channelShidianInfo1.getSaturday();
            channelShidianInfo.setSaturday(saturday);
        }
        if (channelShidianInfo1.getSunday() != null) {
            sunday = channelShidianInfo1.getSunday();
            channelShidianInfo.setSunday(sunday);
        }
        channelShidianInfo.setDoneDate(doneDate);
        channelShidianInfo.setChannelEntityId(channelEntityId);
        /**
         * 1.将变更的字段信息存入返回体中
         * */
        List<JSONObject> dataList1 = new ArrayList<JSONObject>();
        dataList1 = insertReturnMsg(channelEntityName, monday, tuesday, wednesday, thursday, friday, saturday, sunday, unifyCode, reginName, reginCode,
                longitude, latitude, nodeAddr, relationName, relationMobile, email, flag, serviceIds, serviceNames, label);
        dataList.add(dataList1.get(0));
        /**
         * 2.更新试点营业厅信息表
         * */
        channelShidianInfoDao.update(channelShidianInfo);
        return dataList;
    }

    public String DDtoDMS(String d, String d2) {
        String s = "";
        if (s.indexOf(".") == -1) {
            s = 0 + "°" + 0 + "'" + d2;
            return s;
        }
        String[] array = d.split("\\.");
        String degrees = array[0];//度

        Double m = Double.parseDouble("0." + array[1]) * 60;
        String[] array1 = m.toString().split("\\.");
        String minutes = array1[0];
        s = degrees + "°" + minutes + "'" + d2;
        return s;
    }

    public ChannelShidianInfo convertToEntity(String channelEntityName, String monday, String tuesday, String wednesday, String thursday, String friday,
                                              Long channelEntityId, String saturday, String sunday, String unifyCode, String reginName, String reginCode,
                                              String longitude, String latitude, String nodeAddr, String relationName, String relationMobile, String email,
                                              String serviceIds, String serviceNames, Integer label) {
        ChannelShidianInfo channelShidianInfo = new ChannelShidianInfo();
        if (channelEntityName != null) {
            channelShidianInfo.setChannelEntityName(channelEntityName);
        }
        if (label != null) {
            channelShidianInfo.setLabel(label);
        }
        if (channelEntityId != null) {
            channelShidianInfo.setChannelEntityId(channelEntityId);
        }
        if (longitude != null) {
            channelShidianInfo.setLongitude(longitude);
        }
        if (latitude != null) {
            channelShidianInfo.setLatitude(latitude);
        }
        if (nodeAddr != null) {
            channelShidianInfo.setNodeAddr(nodeAddr);
        }
        if (relationName != null) {
            channelShidianInfo.setRelationName(relationName);
        }
        if (relationMobile != null) {
            channelShidianInfo.setRelationMobile(relationMobile);
        }
        if (email != null) {
            channelShidianInfo.setEmail(email);
        }
        if (unifyCode != null) {
            channelShidianInfo.setUnifyCode(unifyCode);
        }
        if (reginCode != null) {
            channelShidianInfo.setReginCode(reginCode);
        }
        if (reginName != null) {
            channelShidianInfo.setReginName(reginName);
        }
        if (serviceNames != null) {
            channelShidianInfo.setServiceName(serviceNames);
        }
        if (serviceIds != null) {
            channelShidianInfo.setServiceId(serviceIds);
        }
        if (monday != null) {
            channelShidianInfo.setMonday(monday);
        }
        if (tuesday != null) {
            channelShidianInfo.setTuesday(tuesday);
        }
        if (wednesday != null) {
            channelShidianInfo.setWednesday(wednesday);
        }
        if (thursday != null) {
            channelShidianInfo.setThursday(thursday);
        }
        if (friday != null) {
            channelShidianInfo.setFriday(friday);
        }
        if (saturday != null) {
            channelShidianInfo.setSaturday(saturday);
        }
        if (sunday != null) {
            channelShidianInfo.setSunday(sunday);
        }
        if (serviceIds != null) {
            channelShidianInfo.setServiceId(serviceIds);
        }
        if (serviceNames != null) {
            channelShidianInfo.setServiceName(serviceNames);
        }
        return channelShidianInfo;
    }

    public String enCodeUtf8(String name) {
        String outStr = "";
        if (name != "" && name != null) {
            for (int i = 0; i < name.length(); i++) {
                int charX = (char) name.charAt(i);
                if (charX >= 19968 && charX <= 171941) {
                    outStr += "\\u" + Integer.toHexString(charX);
                } else {
                    outStr += name.charAt(i);
                }
            }
        }
        return outStr;
    }

    public String convertStyleofindustryPatternsAttrs(String convertStyleofindustryPatternsAttrs) throws Exception {
        List<String> list = new ArrayList<String>();
        StringBuffer sb = new StringBuffer();
        if (convertStyleofindustryPatternsAttrs != null) {
            JSONObject jsonObject = JSONObject.fromObject(convertStyleofindustryPatternsAttrs);
            Map<String, Boolean> map = (Map) jsonObject;

            for (Map.Entry<String, Boolean> entry : map.entrySet()) {
                if (entry.getValue()) {
                    //把值为true的放到list中
                    list.add((String) entry.getKey());
                }
            }

        }
        if (list.size() != 0) {
            for (int key = 0; key < list.size(); key++) {
                String codeName = list.get(key);
                List<ChannelSysBaseType> channelSysBaseTypeList = channelSysBaseTypeDao.getChannelSysBaseType(50136, codeName, 1, null, null);
                if (!CollectionUtils.isEmpty(channelSysBaseTypeList)) {
                    convertStyleofindustryPatternsAttrs = channelSysBaseTypeList.get(0).getCodeNameNls();
                    sb.append(convertStyleofindustryPatternsAttrs);
                    if (key < list.size() - 1) {
                        sb.append(",");
                    }

                }
            }
        }
        convertStyleofindustryPatternsAttrs = sb.toString();
        return convertStyleofindustryPatternsAttrs;
    }

    @Override
    public Map<String, String> queryPointsByAgentName(String param) throws Exception {
        logger.info("根据CRM侧的orgId查询对应接口 开始");
        Map<String, String> map = new HashMap<String, String>();
        String orgId = "";
        ChannelOrgAgent channelOrgAgent2 = null;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            orgId = jsonParam.getString("orgId");

        } catch (Exception e) {
            logger.error("获取入参参数：根据orgId查询对应PT-SH-ESB-CHANNEL-0203接口失败:", e);
            logger.debug("********    根据orgId查询对应接口  结束  ********");
            map.put("error", "入参格式错误");
            return map;
        }
        try {
            //1根据orgId查询实体对应关系
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setOrgId(Long.valueOf(orgId));
            List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgents.size() <= 0) {
                map.put("error", "根据orgId未查询到实体对应关系数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() > 1) {
                map.put("error", "根据orgId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() == 1) {
                channelOrgAgent2 = channelOrgAgents.get(0);
                Long agentId = channelOrgAgent2.getAgentId();//channel_entity_id
                List<ChannelAgentInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.getChannelParentEntityInfo(agentId);
                String channelEntityName = "";
                // Integer agentFlag = 99;//默认为其他
                if (!CollectionUtils.isEmpty(channelEntityBasicInfos)) {
                    channelEntityName = channelEntityBasicInfos.get(0).getFullName();
                    agentId = channelEntityBasicInfos.get(0).getAgentId();
                    map.put("agentName", "" + channelEntityName);
                    Long pointsSysId;
                    /**
                     * 查询是否有积分账户
                     */
                    ChannelPointAccInfo channelPointAccInfo = new ChannelPointAccInfo();
                    channelPointAccInfo.setChannelEntityId(agentId);
                    Integer[] pointsAccStatuss = {ChannelConstants.POINTS_ACC_STATUS_NORMAL, ChannelConstants.POINTS_ACC_STATUS_FROST};
                    channelPointAccInfo.setPointAccStatuss(pointsAccStatuss);
                    channelPointAccInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                    List<ChannelPointAccInfo> channelPointAccInfoList;
                    //查询合作方积分账户
                    try {
                        channelPointAccInfoList = channelPointAccInfoDao.query(channelPointAccInfo);
                    } catch (Exception e) {
                        logger.error("查询代理商积分账户失败！", e);
                        throw new Exception("查询代理商积分账户失败！");
                    }
                    if (1 != channelPointAccInfoList.size()) {
                        logger.error("未查询到该代理商有效的积分账户！");
                    }
                    //查询积分信息主表
                    try {
                        PointsInfo pointsInfo = new PointsInfo();
                        // pointsInfo.setPointsIdType(ChannelConstants.POINTS_ID_TYPE_AGENT);
                        pointsInfo.setPointsId(agentId);
                        List<PointsInfo> pointsInfoList = pointsInfoDao.query(pointsInfo);
                        if (0 == pointsInfoList.size()) {
                            map.put("error", "没有找到该用户的积分信息!");
                        }
                        pointsSysId = pointsInfoList.get(0).getPointsSysId();
                    } catch (Exception e) {
                        logger.error("查询合作方积分失败");
                        map.put("error", "查询合作方积分失败");
                        return map;
                    }
                    //查积分信息子表,当前可兑换积分
                    try {
                        String subInfoTable = getPartion(pointsSysId);

                        PointsSubinfo pointsSubinfo = new PointsSubinfo();
                        pointsSubinfo.setSubInfoTable(subInfoTable);
                        pointsSubinfo.setPointsSysId(pointsSysId);

                        List<PointsSubinfo> pointsSubinfoList = pointsSubinfoDao.query(pointsSubinfo);
                        if (pointsSubinfoList.size() > 0) {
                            Long totalPoints = pointsSubinfoList.get(0).getCurrPoints();
                            map.put("totalPoints", totalPoints.toString());
                        } else {
                            map.put("totalPoints", "0");
                        }
                    } catch (Exception e) {
                        logger.error("查积分信息子表,当前可兑换积分失败", e);
                        map.put("totalPoints", "0");

                    }
                    //当前不可兑换积分，当前不可使用积分
                    try {
                        AgentPointNotUser agentPointNotUser1 = new AgentPointNotUser();
                        agentPointNotUser1.setAgentId(agentId);
                        agentPointNotUser1.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                        List<AgentPointNotUser> agentPointNotUserList = agentPointNotUserDao.query(agentPointNotUser1);
                        if (0 != agentPointNotUserList.size()) {
                            Long currPointNotexc = agentPointNotUserList.get(0).getCurrPointNotExc();
                            Long currPointNotuse = agentPointNotUserList.get(0).getCurrPointNotUse();
                            map.put("currPointNotexc", currPointNotexc == null ? "0" : currPointNotexc.toString());
                            map.put("currPointNotuse", currPointNotuse == null ? "0" : currPointNotuse.toString());
                        } else {
                            map.put("currPointNotexc", "0");
                            map.put("currPointNotuse", "0");
                        }
                    } catch (Exception e) {
                        logger.error("查询当前不可兑换积分，当前不可使用积分", e);
                    }
                    try {
                        AgentPointsExpire agentPointsExpire = new AgentPointsExpire();
                        agentPointsExpire.setAgentId(agentId);
                        //判断但前时间是否大于3月31
                        int currMonth = Integer.parseInt(DateUtil.formatDate(DateUtil.getCurrDate(), "MM"));
                        Date startDate = null;
                        Date endDate = null;
                        if (currMonth < 4) {
                            startDate = DateUtil.getDate(-1, 3, 1);
                            endDate = DateUtil.getDate(0, 2, 31);
                        } else {
                            startDate = DateUtil.getDate(0, 3, 1);
                            endDate = DateUtil.getDate(1, 2, 31);
                        }
                        Integer currProtocolExpirePoints = agentPointsExpireDao.getCurrProtocolExpirePoints(agentId, startDate, endDate);
                        if (currProtocolExpirePoints == null) {
                            map.put("currProtocolExpirePoints", "0");
                        } else {
                            map.put("currProtocolExpirePoints", currProtocolExpirePoints.longValue() + "");
                        }
                    } catch (Exception e) {
                        logger.error("查询本协议到期积分失败", e);
                        map.put("currProtocolExpirePoints", "0");
                    }
                    return map;
                } else {
                    logger.error("未查到符合条件的合作方数据数据信息");
                    map.put("error", "未查到符合条件的合作方数据数据信息!!");
                    return map;
                }
            }
        } catch (Exception e) {
            logger.error("根据orgId查询对应接口失败:", e);
            map.put("error", "根据orgId查询对应接口失败,接口异常！！！");
            return map;
        }
        logger.info("查询申请全网统一编码所需信息，根据orgId查询对应接口失败 结束");
        return map;
    }

    //根据积分流水编号获取对应的分区
    @Override
    public String getPartion(Long pointsSysId) {
        String partion = (pointsSysId % 10) + "_" + DateUtil.formatCurrentDate("yyyy");
        return partion;
    }

    public Map<String, Object> getQueuingInfoList(String param) throws IOException {
        logger.info("取号信息模块信息");
        Map<String, Object> paramMap = JsonUtil.json2Map(param);
        Long orgId = Long.parseLong(paramMap.get("orgId").toString());//获取组织编号
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<JSONObject> queuingInfoList = new ArrayList<JSONObject>();
        try {
            List<QueuingInfo> list = queuingInfoDao.getQueuingInfoList(orgId);
            if (list != null) {
                Map<String, Object> map = new HashMap<String, Object>();
                QueuingInfo queuingInfo = list.get(0);
                if (queuingInfo.getSelectDockingSystem() == null) {
                    Integer SelectDockingSystem = 0;
                    map.put("selectDockingSystem", SelectDockingSystem);
                } else {
                    map.put("selectDockingSystem", queuingInfo.getSelectDockingSystem());//选择对接系统
                }
                if (queuingInfo.getIsZxqh() == null) {
                    Integer isZxqh = 0;
                    map.put("isZxqh", isZxqh);
                } else {
                    map.put("isZxqh", queuingInfo.getIsZxqh());//是否开启在线选号
                }
                if (queuingInfo.getIsYyqh() == null) {
                    Integer isYyqh = 0;
                    map.put("isYyqh", isYyqh);
                } else {
                    map.put("isYyqh", queuingInfo.getIsYyqh());//是否开启预约取号
                }
                if (queuingInfo.getZxqhSetting() == null) {
                    String ZxqhSetting = "";
                    map.put("zxqhSetting", ZxqhSetting);
                } else {
                    map.put("zxqhSetting", queuingInfo.getZxqhSetting());//在线取号配置链接
                }
                if (queuingInfo.getYyqhSetting() == null) {
                    String YyqhSetting = "";
                    map.put("yyqhSetting", YyqhSetting);
                } else {
                    map.put("yyqhSetting", queuingInfo.getYyqhSetting());//预约取号配置链接
                }
                //转为json格式
                JSONObject jsonObject = JSONObject.fromObject(map);
                queuingInfoList.add(jsonObject);
                dataMap.put("bizCode", "0000");//返回码（0000：成功）
                dataMap.put("bizDesc", "数据查询成功");
                dataMap.put("queuingInfos", queuingInfoList);
            }
        } catch (Exception e) {
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "查询渠道库信息时出现异常！！");
        }
        return dataMap;
    }

    @Override
    public Map<String, Object> getQueuingInfoListByName(String param) throws Exception {
        logger.info("取号信息模块信息start-------------");
        Map<String, Object> paramMap = JsonUtil.json2Map(param);
        String channelEntityName =paramMap.get("groupName").toString();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<JSONObject> queuingInfoList = new ArrayList<JSONObject>();
        try {
            if (StringUtils.isNotBlank(channelEntityName)){
                List<QueuingInfo> list = queuingInfoDao.getQueuingInfoListByName(channelEntityName);
                if (list != null) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    QueuingInfo queuingInfo = list.get(0);
                    if (queuingInfo.getSelectDockingSystem() == null) {
                        Integer SelectDockingSystem = 0;
                        map.put("selectDockingSystem", SelectDockingSystem);
                    } else {
                        map.put("selectDockingSystem", queuingInfo.getSelectDockingSystem());//选择对接系统
                    }
                    if (queuingInfo.getZxqhSetting() == null) {
                        String ZxqhSetting = "";
                        map.put("zxqhSetting", ZxqhSetting);
                    } else {
                        map.put("zxqhSetting", queuingInfo.getZxqhSetting());//在线取号配置链接
                    }
                    if (queuingInfo.getNodeName()==null){
                        String nodeName = "";
                        map.put("nodeName", nodeName);
                    }else {
                        map.put("nodeName", queuingInfo.getNodeName());//网点名字
                    }

                    //转为json格式
                    JSONObject jsonObject = JSONObject.fromObject(map);
                    queuingInfoList.add(jsonObject);
                    dataMap.put("bizCode", "0000");//返回码（0000：成功）
                    dataMap.put("bizDesc", "数据查询成功");
                    dataMap.put("queuingInfos", queuingInfoList);
                }
            }else {
                List<QueuingInfo> list = queuingInfoDao.getQueuingInfoListByName2();
                    Map<String, Object> map = new HashMap<String, Object>();
                    for (QueuingInfo queuingInfo:list){
                        if (queuingInfo.getSelectDockingSystem() == null) {
                            Integer SelectDockingSystem = 0;
                            map.put("selectDockingSystem", SelectDockingSystem);
                        } else {
                            map.put("selectDockingSystem", queuingInfo.getSelectDockingSystem());//选择对接系统
                        }
                        if (queuingInfo.getZxqhSetting() == null) {
                            String ZxqhSetting = "";
                            map.put("zxqhSetting", ZxqhSetting);
                        } else {
                            map.put("zxqhSetting", queuingInfo.getZxqhSetting());//在线取号配置链接
                        }
                        if (queuingInfo.getNodeName()==null){
                            String nodeName = "";
                            map.put("nodeName", nodeName);
                        }else {
                            map.put("nodeName", queuingInfo.getNodeName());//网点名字
                        }
                        //转为json格式
                        JSONObject jsonObject = JSONObject.fromObject(map);
                        queuingInfoList.add(jsonObject);
                    }
                    dataMap.put("bizCode", "0000");//返回码（0000：成功）
                    dataMap.put("bizDesc", "数据查询成功");
                    dataMap.put("queuingInfos", queuingInfoList);
            }
        } catch (Exception e) {
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "查询渠道库信息时出现异常！！");
            logger.error("异常",e);
        }
        return dataMap;
    }

    //通过H5接口传合作方录入数据信息落表
    @Override
    public Map<String, Object> setAgentApplicationInfo(String param) throws Exception {
        logger.info("获取H5传合作方录入数据信息接口落表数据start-------------");
        Map<String, Object> paramMap = JsonUtil.json2Map(param);
        Map<String, Object> dataMap = new HashMap<String, Object>();
        String agentId = paramMap.get("agentId").toString();
        String channelEntityName = paramMap.get("channelEntityName").toString();
        String phoneNum = paramMap.get("phoneNum").toString();
        String relationName = paramMap.get("relationName").toString();
        String relationMobile = paramMap.get("relationMobile").toString();
        String relationEmail = paramMap.get("relationEmail").toString();
        String agentRemark = paramMap.get("agentRemark").toString();
        String channelAgentArea = paramMap.get("channelAgentArea").toString();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String createDate = sdf.format(new Date());
            ChannelAgentApplicationInfo channelAgentApplicationInfo = new ChannelAgentApplicationInfo();
            channelAgentApplicationInfo.setAgentId(agentId);
            channelAgentApplicationInfo.setChannelEntityName(channelEntityName);
            channelAgentApplicationInfo.setPhoneNum(phoneNum);
            channelAgentApplicationInfo.setRelationName(relationName);
            channelAgentApplicationInfo.setRelationMobile(relationMobile);
            channelAgentApplicationInfo.setRelationEmail(relationEmail);
            channelAgentApplicationInfo.setAgentRemark(agentRemark);
            channelAgentApplicationInfo.setChannelAgentArea(channelAgentArea);
            channelAgentApplicationInfo.setCreateDate(createDate);
            channelAgentApplicationInfo.setIsDeal(0);
            channelAgentApplicationInfo.setState(1);
            channelAgentApplicationInfoDao.insert(channelAgentApplicationInfo);

            dataMap.put("bizCode", "0000");//返回码（0000：成功）
            dataMap.put("bizDesc", "数据入库成功");
            dataMap.put("agentId", agentId);
        } catch (Exception e) {
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "入库渠道库信息时出现异常！！");
            logger.error("异常", e);
        }
        return dataMap;
    }

    @Override
    public Map<String, Object> setAgentApplicationFile(String param) throws Exception {
        logger.info("获取H5传合作方录入文件数据信息接口落表数据start-------------");
        Map<String, Object> paramMap = JsonUtil.json2Map(param);
        Map<String, Object> dataMap = new HashMap<String, Object>();
        String agentId = paramMap.get("agentId").toString();
        String fileName = paramMap.get("fileName").toString();
        String fileType = paramMap.get("fileType").toString();
        String fileInfo = paramMap.get("fileInfo").toString();
        try {
            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
            String createDate = sf.format(new Date());
            ChannelAgentApplicationFileInfo channelAgentApplicationFileInfo = new ChannelAgentApplicationFileInfo();
            channelAgentApplicationFileInfo.setAgentId(agentId);
            channelAgentApplicationFileInfo.setFileName(fileName);
            channelAgentApplicationFileInfo.setFileType(fileType);
            channelAgentApplicationFileInfo.setFileInfo(fileInfo);
            channelAgentApplicationFileInfo.setCreateDate(createDate);
            channelAgentApplicationFileInfoDao.insert(channelAgentApplicationFileInfo);
            dataMap.put("bizCode", "0000");//返回码（0000：成功）
            dataMap.put("bizDesc", "文件数据入库成功");
            dataMap.put("agentId", agentId);
        } catch (Exception e) {
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "入库渠道库信息时出现异常！！");
            logger.error("异常", e);

        }
        return dataMap;
    }

    @Override
    public Map<String, Object> getAgentApplicationInfo(String param) throws Exception {
        logger.info("根据手机号查询合作方数据信息start-------------");
        Map<String, Object> paramMap = JsonUtil.json2Map(param);
        String phoneNum = paramMap.get("phoneNum").toString();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<JSONObject> queryInfoList = new ArrayList<JSONObject>();
        List<JSONObject> queryInfoFileList = new ArrayList<JSONObject>();
        try {
            List<ChannelAgentApplicationInfo> list = channelAgentApplicationInfoDao.getChannelAgentInfoListByPhoneNum(phoneNum);
            List<ChannelAgentApplicationFileInfo> fileList = channelAgentApplicationFileInfoDao.getChannelAgentFileInfoListByPhoneNum1(phoneNum);

            if (list != null && list.size() !=0) {
                Map<String, Object> map = new HashMap<String, Object>();
                ChannelAgentApplicationInfo channelAgentApplicationInfo = list.get(0);
                map.put("agentId",channelAgentApplicationInfo.getAgentId());
                map.put("channelEntityName",channelAgentApplicationInfo.getChannelEntityName());
                map.put("phoneNum",phoneNum);
                map.put("relationName",channelAgentApplicationInfo.getRelationName());
                map.put("relationMobile",channelAgentApplicationInfo.getRelationMobile());
                map.put("relationEmail",channelAgentApplicationInfo.getRelationEmail());
                map.put("agentRemark",channelAgentApplicationInfo.getAgentRemark());
                map.put("channelAgentArea",channelAgentApplicationInfo.getChannelAgentArea());
                //转为json格式
                JSONObject jsonObject = JSONObject.fromObject(map);
                queryInfoList.add(jsonObject);
                dataMap.put("bizCode", "0000");//返回码（0000：成功）
                dataMap.put("bizDesc", "数据查询成功");
                dataMap.put("queryInfos", queryInfoList);
            } else{
                dataMap.put("bizCode", "0000");//返回码（0001：查询失败）
                dataMap.put("bizDesc", "数据查询失败，渠道库未找到该号码相关信息");
                dataMap.put("queryInfos", "[]");
            }
            if(fileList != null && fileList.size() != 0){
                for (int i = 0; i<fileList.size(); i++){
                    Map<String, Object> map = new HashMap<String, Object>();
                    ChannelAgentApplicationFileInfo channelAgentApplicationFileInfo = fileList.get(i);
                    map.put("agentId",channelAgentApplicationFileInfo.getAgentId());
                    map.put("fileName",channelAgentApplicationFileInfo.getFileName());
                    map.put("fileType",channelAgentApplicationFileInfo.getFileType());
                   //转为json格式
                    JSONObject jsonObject = JSONObject.fromObject(map);
                    queryInfoFileList.add(jsonObject);
                }
                dataMap.put("queryInfoFileList",queryInfoFileList);

            }

        } catch (Exception e) {
            dataMap.put("bizCode", "0001");//返回码（0001：查询失败）
            dataMap.put("bizDesc", "根据手机号码查询渠道库信息时出现异常！！");
            logger.error("异常", e);
        }
        return dataMap;
    }

    @Override
    public Map<String, Object> getAgentApplicationFile(String param) throws Exception {
        logger.info("根据手机号查询合作方文件数据信息start-------------");
        Map<String, Object> paramMap = JsonUtil.json2Map(param);
        String phoneNum = paramMap.get("phoneNum").toString();
        String fileName = paramMap.get("fileName").toString();
        String fileType = paramMap.get("fileType").toString();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<JSONObject> queryInfoList = new ArrayList<JSONObject>();
        try {
            List<ChannelAgentApplicationFileInfo> list = channelAgentApplicationFileInfoDao.getChannelAgentFileInfoListByPhoneNum(phoneNum,fileName,fileType);
            if (list != null) {
                Map<String, Object> map = new HashMap<String, Object>();
                ChannelAgentApplicationFileInfo channelAgentApplicationFileInfo = list.get(0);
                map.put("agentId", channelAgentApplicationFileInfo.getAgentId());
                map.put("fileName", channelAgentApplicationFileInfo.getFileName());
                map.put("fileType", channelAgentApplicationFileInfo.getFileType());
                map.put("fileInfo", channelAgentApplicationFileInfo.getFileInfo());
                //转为json格式
                JSONObject jsonObject = JSONObject.fromObject(map);
                queryInfoList.add(jsonObject);
                dataMap.put("bizCode", "0000");//返回码（0000：成功）
                dataMap.put("bizDesc", "数据查询成功");
                dataMap.put("queryInfos", queryInfoList);
            }else{
                dataMap.put("bizCode", "0000");//返回码（0001：成功）
                dataMap.put("bizDesc", "数据查询失败，渠道库未找到该号码相关信息");
                dataMap.put("queryInfos", "[]");
            }
        } catch (Exception e) {
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "根据手机号码查询渠道库信息时出现异常！！");
            logger.error("异常", e);
        }
        return dataMap;
    }



    @Override
    public Map<String, Object> getChannelNodeBusinessTime(String param) throws Exception {
        logger.info("同步网点营业厅时间 营业状态行政区等字段给H5数据信息start-------------");
        Map<String, Object> paramMap = JsonUtil.json2Map(param);
        String areaId = paramMap.get("areaId").toString();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<JSONObject> queryInfoList = new ArrayList<JSONObject>();
        try {
            List<ChannelNodeDtl> queryList = channelNodeDao.queryChannelNodeBusinessTime(areaId);
            if(queryList != null && queryList.size() != 0){
                for (int i = 0; i<queryList.size(); i++){
                    Map<String, Object> map = new HashMap<String, Object>();
                    ChannelNodeDtl channelNodeDtl = queryList.get(i);

                    List<ChannelEntityRelationInfo> queryRelInfoList= channelEntityRelationInfoDao.getChannelEntityRelationInfo(channelNodeDtl.getNodeId(),1);
                    ChannelEntityRelationInfo channelEntityRelationInfo = queryRelInfoList.get(0);
                    if (com.ailk.newchnl.util.StringUtils.isNullOrBlank(channelEntityRelationInfo.getRelationMobile())){
                        continue;
                    }
                    map.put("relationMobile", channelEntityRelationInfo.getRelationMobile());
                    //将行政区标识转换成文字对应区域
                    Integer regionId = channelNodeDtl.getRegionId();
                    List<ChannelSysBaseType> ChannelSysBaseInfoList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.CHANNEL_SYS_BASE_TYPE_CODETYPE_REGION_ID, regionId, null, null);
                    String regionName = ChannelSysBaseInfoList.get(0).getCodeName();
                    map.put("regionId", regionName);

                    map.put("channelEntityName", channelNodeDtl.getChannelEntityName());
                    map.put("BusiniessTime", channelNodeDtl.getBusinessTime());
                    //营业厅状态标识转换成对应的文字营业状态
                    Integer channelEntityStatu = channelNodeDtl.getChannelEntityStatus();
                    List<ChannelSysBaseType> ChannelSysBaseInfoList1 =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10003, channelEntityStatu, null, null);
                    String channelEntityStatus = ChannelSysBaseInfoList1.get(0).getCodeName();
                    map.put("channelEntityStatus", channelEntityStatus);
                    //转为json格式
                    JSONObject jsonObject = JSONObject.fromObject(map);
                    queryInfoList.add(jsonObject);
                }
                dataMap.put("bizCode", "0000");//返回码（0000：成功）
                dataMap.put("bizDesc", "数据查询成功");
                dataMap.put("queryInfoList",queryInfoList);
            }
        } catch (Exception e) {
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "查询渠道库信息时出现异常！！");
            logger.error("异常", e);
        }
        return dataMap;
    }

    @Override
    public Map<String, Object> getChannelStreetInfo(String param) throws Exception {
        logger.info("根据orgId查询网点对应的网格信息start-------------");
        Map<String, Object> paramMap = JsonUtil.json2Map(param);
        String orgId = paramMap.get("orgId").toString();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        List<JSONObject> queryInfoList = new ArrayList<JSONObject>();
        try {
            String streetId =null;
            if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(orgId)){
                streetId = channelNodeDao.getStreetIdByOrgId(orgId);
            }
            if (streetId != null) {
                List<GridTreeInfo> gridTreeInfoList = null;
                List<GridTreeInfo> gridTreeInfoList1 = null;
                List<GridTreeInfo> gridTreeInfoList2 = null;
                List<GridTreeInfo> gridTreeInfoList3 = null;
                //查找最低一级 三级网格信息
                gridTreeInfoList = gridTreeInfoDao.getGridInfoById(streetId);
                if (gridTreeInfoList != null){
                   Long treeId1 = gridTreeInfoList.get(0).getTreeId();
                   String areaCode1 = gridTreeInfoList.get(0).getAreaCode();
                   String areaLevel1 = gridTreeInfoList.get(0).getAreaLevel();
                   String areaName1 = gridTreeInfoList.get(0).getAreaName();
                   String parentId1 = gridTreeInfoList.get(0).getParentId();
                   Map<String, Object> map1 = new HashMap<String, Object>();
                   map1.put("treeId1", treeId1);
                   map1.put("areaCode1", areaCode1);
                   map1.put("areaLevel1", areaLevel1);
                   map1.put("areaName1", areaName1);
                   map1.put("parentId1", parentId1);
                    //转为json格式
                   JSONObject jsonObject1 = JSONObject.fromObject(map1);
                   queryInfoList.add(jsonObject1);
                   if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(parentId1)){
                       //查找二级网格信息
                       gridTreeInfoList1 = gridTreeInfoDao.getGridInfoById(parentId1);
                       if (gridTreeInfoList1 != null){
                           Long treeId2 = gridTreeInfoList1.get(0).getTreeId();
                           String areaCode2 = gridTreeInfoList1.get(0).getAreaCode();
                           String areaLevel2 = gridTreeInfoList1.get(0).getAreaLevel();
                           String areaName2 = gridTreeInfoList1.get(0).getAreaName();
                           String parentId2 = gridTreeInfoList1.get(0).getParentId();
                           Map<String, Object> map2 = new HashMap<String, Object>();
                           map2.put("treeId2", treeId2);
                           map2.put("areaCode2", areaCode2);
                           map2.put("areaLevel2", areaLevel2);
                           map2.put("areaName2", areaName2);
                           map2.put("parentId2", parentId2);
                           //转为json格式
                           JSONObject jsonObject2 = JSONObject.fromObject(map2);
                           queryInfoList.add(jsonObject2);
                           if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(parentId2)){
                               ////查找三级网格信息
                               gridTreeInfoList2 = gridTreeInfoDao.getGridInfoById(parentId2);
                               if (gridTreeInfoList2 != null){
                                   Long treeId3 = gridTreeInfoList2.get(0).getTreeId();
                                   String areaCode3 = gridTreeInfoList2.get(0).getAreaCode();
                                   String areaLevel3 = gridTreeInfoList2.get(0).getAreaLevel();
                                   String areaName3 = gridTreeInfoList2.get(0).getAreaName();
                                   String parentId3 = gridTreeInfoList2.get(0).getParentId();
                                   Map<String, Object> map3 = new HashMap<String, Object>();
                                   map3.put("treeId3", treeId3);
                                   map3.put("areaCode3", areaCode3);
                                   map3.put("areaLevel3", areaLevel3);
                                   map3.put("areaName3", areaName3);
                                   map3.put("parentId3", parentId3);
                                   //转为json格式
                                   JSONObject jsonObject3 = JSONObject.fromObject(map3);
                                   queryInfoList.add(jsonObject3);
                                   if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(parentId3) && !parentId3.equals("-1")){
                                       gridTreeInfoList3 = gridTreeInfoDao.getGridInfoById(parentId3);
                                       if (gridTreeInfoList3 != null){
                                           Long treeId4 = gridTreeInfoList3.get(0).getTreeId();
                                           String areaCode4 = gridTreeInfoList3.get(0).getAreaCode();
                                           String areaLevel4 = gridTreeInfoList3.get(0).getAreaLevel();
                                           String areaName4 = gridTreeInfoList3.get(0).getAreaName();
                                           String parentId4 = gridTreeInfoList3.get(0).getParentId();
                                           Map<String, Object> map4 = new HashMap<String, Object>();
                                           map4.put("treeId4", treeId4);
                                           map4.put("areaCode4", areaCode4);
                                           map4.put("areaLevel4", areaLevel4);
                                           map4.put("areaName4", areaName4);
                                           map4.put("parentId4", parentId4);
                                           //转为json格式
                                           JSONObject jsonObject4 = JSONObject.fromObject(map4);
                                           queryInfoList.add(jsonObject4);
                                       }
                                   }else{
                                       logger.info("无对应的网格层级为3的网格信息！！！");
                                   }
                               }
                           }
                       }
                    }
                }
                logger.info("根据orgId查询网点对应的网格信息end-------------");
                dataMap.put("bizCode", "0000");//返回码（0000：成功）
                dataMap.put("bizDesc", "数据查询成功");
                dataMap.put("queryInfos", queryInfoList);
            }else{
                dataMap.put("bizCode", "0000");//返回码（0001：成功）
                dataMap.put("bizDesc", "数据查询失败，渠道库未找到该orgId组织id相关信息");
                dataMap.put("queryInfos", "[]");
            }
        } catch (Exception e) {
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "根据orgId组织id查询渠道库信息时出现异常！！");
            logger.error("异常", e);
        }
        return dataMap;
    }

    @Override
    public Map<String, Object> getChannelShareholderInfo(String param) throws Exception {

        logger.info("股东认证异常回传信息入库start-------------");
        Map<String, Object> paramMap = JsonUtil.json2Map(param);
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


        Map<String, Object> dataMap = new HashMap<String, Object>();
        try {
            //获取传参入库
            String deliveryTime = paramMap.get("deliveryTime").toString();
            String proCode = paramMap.get("proCode").toString();
            String socIden = paramMap.get("socIden").toString();
            String shareName = paramMap.get("shareName").toString();
            String docuType = paramMap.get("docuType").toString();
            String docuInfo = paramMap.get("docuInfo").toString();
            docuInfo = docuInfo.replace(" ","+");
            String isComHum = paramMap.get("isComHum").toString();
            String isComKinsman = paramMap.get("isComKinsman").toString();
            String kinEmployeeName = paramMap.get("kinEmployeeName").toString();
            String employeeId = paramMap.get("employeeId").toString();
            String isComMarketHum = paramMap.get("isComMarketHum").toString();
            String isProHum = paramMap.get("isProHum").toString();
            String emplOrgCompany = paramMap.get("emplOrgCompany") == null ? "" : paramMap.get("emplOrgCompany").toString();

//            String referChannelEncoding = paramMap.get("referChannelEncoding").toString();
//            String channelName = paramMap.get("channelName").toString();
//            String channelTrait = paramMap.get("channelTrait").toString();
            String referChannelEncoding ="";
            String channelName ="";
            String channelTrait ="";

            if(docuType.equals("1")){
                docuType="身份证";
            }else if(docuType.equals("2")){
                docuType="护照";
            }else if(docuType.equals("3")){
                docuType="军官证";
            }else if(docuType.equals("4")){
                docuType="驾驶证";
            }else{
                logger.error("证件类型输入异常 值为"+docuType);
                throw new Exception("证件类型输入异常");
            }

            if(isComHum.equals("1")){
                isComHum="是";
            }else if(isComHum.equals("2")){
                isComHum="否";
            }else{
                logger.error("解析是否我司员工异常 值为"+isComHum);
                throw new Exception("解析是否我司员工异常 值为"+isComHum);
            }

            if(isComKinsman.equals("1")){
                isComKinsman="是";
            }else if(isComKinsman.equals("2")){
                isComKinsman="否";
            }else{
                logger.error("解析是否为我司员工直系亲属异常 值为"+isComKinsman);
                throw new Exception("解析是否为我司员工直系亲属异常 值为"+isComKinsman);
            }

            if(isComMarketHum.equals("1")){
                isComMarketHum="是";
            }else if(isComMarketHum.equals("2")){
                isComMarketHum="否";
            }else {
                logger.error("解析是否为我公司市场条线员工异常 值为"+isComMarketHum);
                throw new Exception("解析是否为我公司市场条线员工异常 值为"+isComMarketHum);
            }

            if(isProHum.equals("1")||isProHum.equals("是")){
                isProHum="是";
            }else if(isProHum.equals("2")){
                isProHum="否";
            }else {
                logger.error("解析是否为本省员工异常 值为"+isProHum);
                throw new Exception("解析是否为本省员工异常 值为"+isProHum);
            }

//          判断为空
//            if(com.ailk.newchnl.util.StringUtils.isNullOrBlank(proCode)||com.ailk.newchnl.util.StringUtils.isNullOrBlank(socIden)||
//                    com.ailk.newchnl.util.StringUtils.isNullOrBlank(shareName)||
//                    com.ailk.newchnl.util.StringUtils.isNullOrBlank(docuInfo)||
//                    com.ailk.newchnl.util.StringUtils.isNullOrBlank(employeeId)||
//                    com.ailk.newchnl.util.StringUtils.isNullOrBlank(deliveryTime)){
//                logger.info("传入数据之中有必填项为空");
//                throw new Exception("传入数据之中有必填项为空");
//            }
//            解密
            List<ChannelSysBaseType> channelSysBaseTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(50034, 109, null, null);
            String password = channelSysBaseTypeList.get(0).getCodeName();
            logger.info("新加密方式证件编号:" + docuInfo);
            logger.info("密钥:" + password);
            String sa = AESUtils.decrypt(docuInfo, password);
            logger.info("解密出的证件编号:" + sa);

            //入表
            ShareholderApproveInfo shareholderApproveInfo = new ShareholderApproveInfo();
            Date deliveryTimeDate = ft.parse(deliveryTime);
            shareholderApproveInfo.setDeliveryTime(deliveryTimeDate);
            shareholderApproveInfo.setProCode(proCode);
            shareholderApproveInfo.setSocIden(socIden);
            shareholderApproveInfo.setShareName(shareName);
            shareholderApproveInfo.setDocuType(docuType);
            shareholderApproveInfo.setDocuInfo(sa);
            shareholderApproveInfo.setIsComHum(isComHum);
            shareholderApproveInfo.setIsComKinsman(isComKinsman);
            shareholderApproveInfo.setKinEmployeeName(kinEmployeeName);
            shareholderApproveInfo.setEmployeeId(employeeId);
            shareholderApproveInfo.setIsComMarketHum(isComMarketHum);
            shareholderApproveInfo.setIsProHum(isProHum);
            shareholderApproveInfo.setExit1(emplOrgCompany);

            //解析channelINfo根据涉及渠道编码个数来入表
//            String channelInfo = paramMap.get("channelInfo").toString();
//            com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(paramMap.toString());
//            com.alibaba.fastjson.JSONArray channelInfoArray = jsonObject.getJSONArray("channelInfo");
//            List channelInfoArray = jsonObject.getJSONArray("channelInfo");
            List channelInfoArray = (List) JSONArray.toList( JSONObject.fromObject(paramMap).getJSONArray("channelInfo"));
            if(channelInfoArray.size()>0){
                for (int i = 0; i < channelInfoArray.size(); i++) {
                    String temp = (String) channelInfoArray.get(i);
                    String[] list1 = new String[3];
                    list1 = temp.split(",");
                    referChannelEncoding = list1[0] == null ? "" : list1[0];
                    channelName = list1[1] == null ? "" : list1[1];
                    channelTrait = list1[2] == null ? "" : list1[2];
                    shareholderApproveInfo.setReferChannelEncoding(referChannelEncoding);
                    shareholderApproveInfo.setChannelName(channelName);
                    shareholderApproveInfo.setChannelTrait(channelTrait);
                    shareholderApproveInfoDao.insert(shareholderApproveInfo);
                }
            }else{
                //shareholderApproveInfoDao.insert(shareholderApproveInfo);
                logger.info("channelInfo为必填项，数据异常");
                throw new Exception("channelInfo为必填项，数据异常");
            }
            dataMap.put("bizCode", "0000");//返回码（0000：成功）
            dataMap.put("bizDesc", "数据入库成功");

        } catch (Exception e) {
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "股东认证回传信息入库异常！！");
            logger.error("异常", e);
        }
        return dataMap;
    }

    @Override
    public Map<String, Object> getChannelNodeInfoByOrgId(String param) throws Exception {
        logger.info("-----机构信息同步接口 通过orgId 同步门店电话号码、负责人、地址 begin------");
        Map<String, Object> paramMap = JsonUtil.json2Map(param);
        String orgId = paramMap.get("orgId").toString().trim();
        Map<String, Object> dataMap = new HashMap<String, Object>();
        Map<String, Object> map = new HashMap<String, Object>();
        List<JSONObject> queryInfoList = new ArrayList<JSONObject>();
        ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
        channelOrgAgent.setOrgId(Long.parseLong(orgId));
        String orgPhone = "";
        String principalName = "";
        String orgAddr = "";
        try {
            List<ChannelOrgAgent> channelOrgAgentList =  channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgentList.size() == 1){
                Long nodeId = channelOrgAgentList.get(0).getAgentId();
                List<ChannelNodeDtl> channelNodeInfo = channelNodeDao.getChannelNodeInfo(nodeId);
                // 获得联系人信息
                List<ChannelEntityRelationInfo> channelEntityRelationInfoList = channelEntityRelationInfoDao.getChannelEntityRelationInfo(
                        nodeId, ChannelConstants.RELATION_TYPE_CONTACTS);
                if (channelNodeInfo.size() == 1 && channelEntityRelationInfoList.size() == 1){
                    orgPhone = channelEntityRelationInfoList.get(0).getRelationMobile();     //厅经理联系电话
                    principalName = channelEntityRelationInfoList.get(0).getRelationName();  //厅经理
                    orgAddr = channelNodeInfo.get(0).getNodeAddr();                 //门店地址
                    map.put("orgPhone", orgPhone);
                    map.put("principalName", principalName);
                    map.put("orgAddr", orgAddr);
                    JSONObject js = JSONObject.fromObject(map);
                    queryInfoList.add(js);
                    logger.info("--------机构信息同步接口 通过orgId 同步门店电话号码、负责人、地址 end--------");
                    dataMap.put("bizCode", "0000");//返回码（0000：成功）
                    dataMap.put("bizDesc", "数据查询成功");
                    dataMap.put("queryInfos", queryInfoList);
                }else{
                    logger.info("该组织id:" + orgId + ",所对应的门店信息不存在或已退出！！！");
                    throw new Exception("该组织id:" + orgId + ",所对应的门店信息不存在或已退出！！！");
                }
            }else{
                logger.error("该组织id:" + orgId + ",所对应的门店信息不存在或已退出！！！");
                throw new Exception("该组织id:" + orgId + ",所对应的门店信息不存在或已退出！！！");
            }
        }catch (Exception e){
            dataMap.put("bizCode", "0001");//返回码（0001：失败）
            dataMap.put("bizDesc", "机构信息同步接口查询异常！！"+ e.getMessage());
            logger.error("机构信息同步接口查询异常", e);
        }

        return dataMap;
    }

    @Override
    public ChannelJson getChannelBusiToCrmJson(ChannelJson channelJson, String param, int index) throws Exception {
        int dataLength = index;
        int length = param.length();
        if (length < dataLength){
            channelJson.setJson1Str(param);
            return channelJson;
        }
        channelJson.setJson1Str(param.substring(0, index));
        int k = 1;
        if ((length >= dataLength * k) && (length < dataLength * (k + 1))) {
            channelJson.setJson2Str(param.substring(dataLength * k, length));
            return channelJson;
        }
        channelJson.setJson2Str(param.substring(dataLength * k, dataLength * (k + 1)));
        ++k;
        if ((length >= dataLength * k) && (length < dataLength * (k + 1))) {
            channelJson.setJson3Str(param.substring(dataLength * k, length));
            return channelJson;
        }
        channelJson.setJson3Str(param.substring(dataLength * k, dataLength * (k + 1)));
        ++k;
        if ((length >= dataLength * k) && (length < dataLength * (k + 1))) {
            channelJson.setJson4Str(param.substring(dataLength * k, length));
            return channelJson;
        }
        channelJson.setJson4Str(param.substring(dataLength * k, dataLength * (k + 1)));
        ++k;
        if ((length >= dataLength * k) && (length < dataLength * (k + 1))) {
            channelJson.setJson5Str(param.substring(dataLength * k, length));
            return channelJson;
        }
        channelJson.setJson5Str(param.substring(dataLength * k, dataLength * (k + 1)));
        ++k;
        if ((length >= dataLength * k) && (length < dataLength * (k + 1))) {
            channelJson.setJson6Str(param.substring(dataLength * k, length));
            return channelJson;
        }
        channelJson.setJson6Str(param.substring(dataLength * k, dataLength * (k + 1)));
        ++k;
        if ((length >= dataLength * k) && (length < dataLength * (k + 1))) {
            channelJson.setJson7Str(param.substring(dataLength * k, length));
            return channelJson;
        }
        channelJson.setJson7Str(param.substring(dataLength * k, dataLength * (k + 1)));
        ++k;
        if ((length >= dataLength * k) && (length < dataLength * (k + 1))) {
            channelJson.setJson8Str(param.substring(dataLength * k, length));
            return channelJson;
        }
        channelJson.setJson8Str(param.substring(dataLength * k, dataLength * (k + 1)));
        ++k;
        if ((length >= dataLength * k) && (length < dataLength * (k + 1))) {
            channelJson.setJson9Str(param.substring(dataLength * k, length));
            return channelJson;
        }
        channelJson.setJson9Str(param.substring(dataLength * k, dataLength * (k + 1)));
        ++k;
        if ((length >= dataLength * k) && (length < dataLength * (k + 1))) {
            channelJson.setJson10Str(param.substring(dataLength * k, length));
            return channelJson;
        }
        channelJson.setJson10Str(param.substring(dataLength * k, dataLength * (k + 1)));
        ++k;

        return channelJson;
    }

    @Override
    public Map<String, Object> getUnifyCodeByNodeId(String param) throws Exception {
        logger.debug("********    getUnifyCodeByNodeId  begin  ********");
        Map<String, Object> map = new HashMap<String, Object>();
        Long nodeId = 0L;
        String nodeName=null;
        ChannelNode channelNode = new ChannelNode();
        ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();

        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            String temp = jsonParam.getString("node_id").trim();
            String temp2 = jsonParam.getString("qdmc").trim();
            if (temp.equals("") && temp2.equals("")) {
                map.put("unifyCode", "0");
                return map;
            }else if(!temp.equals("")) {
                nodeId = Long.parseLong(temp);
                channelNode.setNodeId(nodeId);
                channelNode.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            }else if (temp.equals("") && !temp2.equals("")){
                nodeName=temp2;
                channelEntityBasicInfo.setChannelEntityName(nodeName);
                channelEntityBasicInfo.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            }
        } catch (Exception e) {
            logger.error("根据网点编号NODE_ID查询全网编码号失败：" + e.getMessage());
            logger.debug("********    getUnifyCodeByNodeId  end  ********");
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String format = sdf.format(date);
            map.put("bizCode", "0010");
            map.put("bizDesc", "根据网点编号/网点名称查询全网编码号失败");
            map.put("oprTime",format);
            return map;
            //			throw new Exception("未传入相应参数或传参格式不正确");
        }
        String e1 = "没有网点编号/网点名称所对应的全网编码";
        List<ChannelEntityBasicInfo> channelBasicInfo = null;
        List<ChannelNode> channelNodeList = null;
        try {
            if(nodeName!=null && !nodeName.equals("")){
                channelBasicInfo=channelEntityBasicInfoDao.getChannelEntityStatusByName(nodeName);
                if(channelBasicInfo.size()!=0) {
                    channelNode.setNodeId(channelBasicInfo.get(0).getChannelEntityId());
                    channelNode.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                }else {
                    throw new Exception(e1);
                }
            }
            if(channelNode.getNodeId()!=0L) {
                channelNodeList = channelNodeDao.query(channelNode);
            }
            String unifyCode=null;
            if (channelNodeList.size() != 0) {
                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                String format = sdf.format(date);
                map.put("bizCode", "0000");
                map.put("unify_code", channelNodeList.get(0).getUnifyCode());
                map.put("oprTime",format);
            }else {
                throw new Exception(e1);
            }
        } catch (Exception e) {
            logger.error("根据网点编号/网点名称查询全网编码失败：" + e.getMessage());
            logger.debug("********    getUnifyCodeByNodeId  end  ********");
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String format = sdf.format(date);
            map.put("bizCode", "0010");
            map.put("bizDesc", e.getMessage());
            map.put("oprTime",format);
        }

        return map;
    }


    @Override
    public Map<String, Object> getAgentByOrgId(String param) throws Exception {
        logger.info("根据CRM侧的orgId查询对应接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        String orgId = "";
        ChannelOrgAgent channelOrgAgent2 = null;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            orgId = jsonParam.getString("orgId");

        } catch (Exception e) {
            logger.error("获取入参参数：根据orgId查询对应PT-SH-FS-OI1209接口失败:", e);
            logger.debug("********    根据orgId查询对应接口  结束  ********");
            map.put("error", "入参格式错误");
            return map;
        }
        try {
            //1根据orgId查询实体对应关系
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setOrgId(Long.valueOf(orgId));
            List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgents.size() <= 0) {
                map.put("error", "根据orgId未查询到实体对应关系数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() > 1) {
                map.put("error", "根据orgId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() == 1) {
                channelOrgAgent2 = channelOrgAgents.get(0);
                Long nodeId = channelOrgAgent2.getAgentId();
                List<ChannelEntityBasicInfo> channelEntityBasicInfos = channelEntityBasicInfoDao.getChannelEntityBasecInfoAgentId(nodeId);
                String channelEntityName = "";
                if (!CollectionUtils.isEmpty(channelEntityBasicInfos)) {
                    map.put("agentId",channelEntityBasicInfos.get(0).getChannelEntityId());
                    map.put("agentName",channelEntityBasicInfos.get(0).getChannelEntityName());
                } else {
                    map.put("error", "未查到符合条件的合作方数据数据信息");
                }
            }
        } catch (Exception e) {
            logger.error("根据orgId查询对应接口失败:", e);
            map.put("error", "根据orgId查询对应接口失败,接口异常！！！");
            return map;
        }
        logger.info("查询申请全网统一编码所需信息，根据orgId查询对应接口失败 结束");
        return map;
    }

    @Override
    public Map<String, Object> getOrgInfoByOrgId(String param) throws Exception {
        logger.info("根据CRM侧的orgId查询对应接口 开始");
        Map<String, Object> map = new HashMap<String, Object>();
        String orgId = "";
        ChannelOrgAgent channelOrgAgent2 = null;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            orgId = jsonParam.getString("orgId");

        } catch (Exception e) {
            logger.error("获取入参参数：根据orgId查询对应接口失败:", e);
            logger.debug("********    根据orgId查询对应接口  结束  ********");
            map.put("error", "入参格式错误");
            return map;
        }
        try {
            //1根据orgId查询实体对应关系
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setOrgId(Long.valueOf(orgId));
            List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgents.size() <= 0) {
                map.put("resultMsg", "根据orgId未查询到实体对应关系数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() > 1) {
                map.put("resultMsg", "根据orgId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
                return map;
            } else if (channelOrgAgents.size() == 1) {
                channelOrgAgent2 = channelOrgAgents.get(0);
            }
            //2查询获取网点信息
            long nodeId = channelOrgAgent2.getAgentId();
            String regionId = "";
            String regionName = "";
            String longitude = "";
            String latitude = "";
            String agentId = "";
            String agentName = "";
            String districtId = "";
            String districtName = "";
            String streetId = "";
            String streetName = "";
            
            List<ChannelNode> channelNodeList = channelNodeDao.getNodeInfo(nodeId);
            if (channelNodeList.size() == 1) {
                ChannelNode channelNode = channelNodeList.get(0);
                longitude = channelNode.getLongitude() == null ? "" : channelNode.getLongitude();
                if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(longitude)) {
                    BigDecimal bigDecimal = new BigDecimal(longitude);
                    longitude = bigDecimal.setScale(6, BigDecimal.ROUND_HALF_UP).toString();
                }
                latitude = channelNode.getLatitude() == null ? "" : channelNode.getLatitude();
                if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(latitude)) {
                    BigDecimal bigDecimal = new BigDecimal(latitude);
                    latitude = bigDecimal.setScale(6, BigDecimal.ROUND_HALF_UP).toString();
                }
                if (channelNode.getStreetId() != null) {
                    streetId = channelNode.getStreetId()+"";//归属网格编号
                }
                if (channelNode.getGridName() != null) {
                    streetName = channelNode.getGridName();//归属网格名称
                }

                ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
                channelEntityBasicInfo.setChannelEntityId(nodeId);
                List<ChannelEntityBasicInfo> channelETBasicInfos = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
                if ((!CollectionUtils.isEmpty(channelETBasicInfos)) && (channelETBasicInfos.size() == 1)) {
                    ChannelEntityBasicInfo channelETBasicInfo =  channelETBasicInfos.get(0);
                    districtId = channelETBasicInfo.getDistrictId()+"";
                    regionId = channelETBasicInfo.getRegionId()+"";

//                    Map<String, String> districtNameMap = ChannelSysBaseTypeUtil.codeNameMap(10002);
//                    if (!districtNameMap.isEmpty()) {
//                        districtName = districtNameMap.get(districtId.toString());
//                    }else {
//                        logger.equals("校验获取归属组织失败或校验获取归属组织不是唯一的！");
//                    }
//                    
//                    Map<String, String> nameMap = ChannelSysBaseTypeUtil.codeNameMap(10023);
//                    if (!nameMap.isEmpty()) {
//                        regionName = nameMap.get(regionId.toString());
//                    }else {
//                        logger.equals("校验获取行政区失败或校验获取行政区不是唯一的！");
//                    }
                    

                    List<ChannelSysBaseType> channelSysBaseTypeList = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10002, channelETBasicInfo.getDistrictId(), null, null);
                    if ((!CollectionUtils.isEmpty(channelSysBaseTypeList)) && (channelSysBaseTypeList.size() == 1)) {
                        districtName = channelSysBaseTypeList.get(0).getCodeName();//根据枚举编号获取到对应的枚举值
                    }else {
                        logger.equals("校验获取归属组织失败或校验获取归属组织不是唯一的！");
                    }
                }else {
                    logger.equals("校验获取网点信息失败或校验获取网点信息不是唯一的！");
                }

                List<ChannelEntityRelInfo> channelEntityRelInfoList = channelEntityRelInfoDao.getParentEntity(nodeId);
                if (!channelEntityRelInfoList.isEmpty()) {
                    agentId = channelEntityRelInfoList.get(0).getParentEntity()+"";
                    ChannelEntityBasicInfo channelAgentInfo = new ChannelEntityBasicInfo();
                    channelAgentInfo.setChannelEntityId(channelEntityRelInfoList.get(0).getParentEntity());
                    List<ChannelEntityBasicInfo> channelAgentInfos = channelEntityBasicInfoDao.query(channelAgentInfo);
                    if ((!CollectionUtils.isEmpty(channelAgentInfos)) && (channelAgentInfos.size() == 1)) {
                        agentName = channelAgentInfos.get(0).getChannelEntityName();
                    }
                }else {
                    logger.equals("校验获取代理商信息失败或校验获取代理商信息不是唯一的！");
                }
                    
                map.put("coordinateSystem", "1");//经纬度坐标系
                map.put("longitude", longitude); //经度
                map.put("latitude", latitude); //纬度
                map.put("regionId", regionId);
                map.put("regionName", regionName);//行政区
                map.put("agentId",agentId);//代理商
                map.put("agentName",agentName);//代理商名称
                map.put("districtId",districtId);//归属区域
                map.put("districtName",districtName);//归属区域
                map.put("streetName",streetName);//归属网格
                map.put("streetId",streetId);
            } else {
                map.put("error", "未查到符合条件的网点数据信息");
            }
        } catch (Exception e) {
            logger.error("根据orgId查询对应接口失败:", e);
            map.put("error", "根据orgId查询对应接口失败,接口异常！！！");
            return map;
        }
        logger.info("查询网格通电子围栏网点所需信息，根据orgId查询对应接口结束");
        return map;
    }

    @Override
    public List<Map<String, Object>> getChannelNodeByOrgIdAndName(String param) throws Exception {
        logger.info("根据CRM传过来的orgId和orgName查询渠道网点对应信息 开始");
        //创建返回的list
        List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
        Map<String, Object> map = new HashMap<String, Object>();
        String opId = "";
        String orgId = "";
        String orgName = "";
        ChannelOrgAgent channelOrgAgent2 = null;
        try {
            JSONObject jsonParam = JSONObject.fromObject(param);
            opId = jsonParam.optString("opId","");
            orgId = jsonParam.getString("orgId");
            orgName = jsonParam.optString("orgName","");
            logger.info("接口入参 - opId: {}, orgId: {}, orgName: {}", opId, orgId, orgName);
        } catch (Exception e) {
            logger.error("获取入参参数：根据orgId和orgName查询对应接口失败:", e);
            logger.debug("********    根据orgId和orgName查询对应接口  结束  ********");
            map.put("error", "入参格式错误");
            resultList.add(map);
            return resultList;
        }
        try {
            // 1、查询入参员工网点对应的所属分公司(districtId)
            Integer districtId = null;
            ChannelOrgAgent channelOrgAgent = new ChannelOrgAgent();
            channelOrgAgent.setOrgId(Long.valueOf(orgId));
            List<ChannelOrgAgent> channelOrgAgents = channelOrgAgentDao.query(channelOrgAgent);
            if (channelOrgAgents.isEmpty()) {
                logger.info("根据orgId未查询到实体对应关系数据，请查证,入参orgId：" + orgId);
                map.put("error", "根据orgId未查询到实体对应关系数据，请查证,入参orgId：" + orgId);
                resultList.add(map);
                return resultList;
            }
            if (channelOrgAgents.size() > 1) {
                logger.info("根据orgId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
                map.put("error", "根据orgId查询实体对应关系出现重复数据，请查证,入参orgId：" + orgId);
                resultList.add(map);
                return resultList;
            }else if (channelOrgAgents.size() == 1) {
                channelOrgAgent2  = channelOrgAgents.get(0);
            }
            long agentId = channelOrgAgent2.getAgentId();
            ChannelEntityBasicInfo channelEntityBasicInfo = new ChannelEntityBasicInfo();
            channelEntityBasicInfo.setChannelEntityId(agentId);
            List<ChannelEntityBasicInfo> channelEntityBasicInfoList = channelEntityBasicInfoDao.query(channelEntityBasicInfo);
            if (!channelEntityBasicInfoList.isEmpty()) {
                districtId = channelEntityBasicInfoList.get(0).getDistrictId();
                logger.info("查询到分公司ID: {}", districtId);
            } else {
                logger.info("根据agentId未查询到渠道实体基础信息，agentId：" + agentId);
                map.put("error", "根据agentId未查询到渠道实体基础信息，agentId：" + agentId);
                resultList.add(map);
                return resultList;
            }

            if (districtId == null) {
                logger.info("查询到的分公司ID为空，agentId：" + agentId);
                map.put("error", "查询到的分公司ID为空，agentId：" + agentId);
                resultList.add(map);
                return resultList;
            }

            // 2、查询所属分公司下面的营业厅(四级渠道为直营店和加盟营业厅)
            ChannelEntityBasicInfo queryEntity = new ChannelEntityBasicInfo();
            queryEntity.setDistrictId(districtId);
            queryEntity.setChannelEntityType((int) ChannelConstants.NODE_OF_ENTITY_TYPE); // 网点类型
            queryEntity.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
            // 设置有效的渠道状态
            Integer[] channelEntityStatuss = {
                    ChannelConstants.CHANNEL_ENTITY_STATUS_PERMIT,
                    ChannelConstants.CHANNEL_ENTITY_STATUS_NORMAL_OPERATION,
                    ChannelConstants.CHANNEL_ENTITY_STATUS_CLOSE_UP_SHOP,
                    ChannelConstants.CHANNEL_ENTITY_STATUS_PAUSE_BUSINESS
            };
            queryEntity.setChannelEntityStatuss(channelEntityStatuss);

            // 先查询所有网点
            List<ChannelEntityBasicInfo> allChannelEntityList = channelEntityBasicInfoDao.query(queryEntity);
            if (allChannelEntityList.isEmpty()) {
                logger.info("根据districtId未查询到对应的网点数据，districtId：" + districtId);
                return resultList;
            }

            // 过滤出四级渠道为直营店和加盟营业厅的网点
            List<ChannelEntityBasicInfo> filteredEntities = new ArrayList<ChannelEntityBasicInfo>();
            for (ChannelEntityBasicInfo entity : allChannelEntityList) {
                ChannelNode channelNode = new ChannelNode();
                channelNode.setNodeId(entity.getChannelEntityId());
                channelNode.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                List<ChannelNode> channelNodeList = channelNodeDao.query(channelNode);
                if (!channelNodeList.isEmpty()) {
                    Integer nodeKind = channelNodeList.get(0).getNodeKind();
                    // 1：直营营业厅、2：加盟营业厅
                    if (nodeKind != null && (nodeKind.equals(ChannelConstants.NODE_KIND_DIRECT_SALES) ||
                            nodeKind.equals(ChannelConstants.NODE_KIND_JOIN_BUSINESS))) {
                        // 如果门店名称不为空，则进行模糊查询过滤
                        if (StringUtils.isNotBlank(orgName)) {
                            if (entity.getChannelEntityName() != null &&
                                entity.getChannelEntityName().contains(orgName)) {
                                filteredEntities.add(entity);
                            }
                        } else {
                            filteredEntities.add(entity);
                        }
                    }
                }
            }

            if (filteredEntities.isEmpty()) {
                logger.info("根据条件未查询到符合要求的营业厅数据，districtId：{}，orgName：{}", districtId, orgName);
                return resultList;
            }
            // 3、查询营业厅对应的详细信息并拼装出参
            for (ChannelEntityBasicInfo entity : filteredEntities) {
                Map<String, Object> nodeInfo = new HashMap<String, Object>();

                // 查询网点详细信息
                ChannelNode channelNodeQuery = new ChannelNode();
                channelNodeQuery.setNodeId(entity.getChannelEntityId());
                channelNodeQuery.setRecStatus(ChannelConstants.REC_STATUS_EFFECTIVE);
                List<ChannelNode> channelNodesList = channelNodeDao.query(channelNodeQuery);

                if (!channelNodesList.isEmpty()) {
                    ChannelNode channelNode = channelNodesList.get(0);

                    // 门店名称
                    nodeInfo.put("channelEntityName", entity.getChannelEntityName() != null ? entity.getChannelEntityName() : "");
                    //门店id
                    String channelEntityId = String.valueOf(entity.getChannelEntityId());
                    nodeInfo.put("channelEntityId",channelEntityId);

                    // 门店状态
                    String channelEntityStatusName = "";
                    if (entity.getChannelEntityStatus() != null) {
                        switch (entity.getChannelEntityStatus()) {
                            case 3: channelEntityStatusName = "准入"; break;
                            case 11: channelEntityStatusName = "正常运营"; break;
                            case 12: channelEntityStatusName = "已关店"; break;
                            case 13: channelEntityStatusName = "暂停营业"; break;
                            case 4: channelEntityStatusName = "预销户"; break;
                            default: channelEntityStatusName = "未知状态"; break;
                        }
                    }
                    nodeInfo.put("channelEntityStatus", channelEntityStatusName);

                    // 四级渠道分类
                    String nodeKindName = "";
                    if (channelNode.getNodeKind() != null) {
                        switch (channelNode.getNodeKind()) {
                            case 1: nodeKindName = "直营营业厅"; break;
                            case 2: nodeKindName = "加盟营业厅"; break;
                            case 3: nodeKindName = "加盟社会店"; break;
                            case 4: nodeKindName = "手机专卖店"; break;
                            case 5: nodeKindName = "授权代理店"; break;
                            case 6: nodeKindName = "手机卖场"; break;
                            case 7: nodeKindName = "自助终端"; break;
                            case 8: nodeKindName = "校园直销队"; break;
                            case 9: nodeKindName = "家庭业务代理店"; break;
                            case 10: nodeKindName = "其它网点"; break;
                            default: nodeKindName = "未知类型"; break;
                        }
                    }
                    nodeInfo.put("nodeKindName", nodeKindName);

                    // 归属区域
                    String districtName = "";
                    if (entity.getDistrictId() != null) {
                        try {
                            ChannelSysBaseType queryParam = new ChannelSysBaseType();
                            queryParam.setCodeType(10002);
                            queryParam.setCodeId(entity.getDistrictId());
                            List<ChannelSysBaseType> districtList = channelSysBaseTypeDao.query(queryParam);
                            districtName = districtList.get(0).getCodeName();
                        } catch (Exception e) {
                            logger.warn("查询归属区域名称失败，districtId：{}", entity.getDistrictId(), e);
                        }
                    }
                    nodeInfo.put("districtName", districtName);

                    //归属区域id
                    String districtIds = String.valueOf(entity.getDistrictId());
                    nodeInfo.put("districtId",districtIds);

                    // 门店地址
                    nodeInfo.put("nodeAddr", channelNode.getNodeAddr() != null ? channelNode.getNodeAddr() : "");

                    // 宽带业务 - 从ext3字段中解析
                    String broadbandBusiness = "无";
                    String ext3 = channelNode.getExt3();
                    if (StringUtils.isNotBlank(ext3)) {
                        try {
                            JSONObject json = JSONObject.fromObject(ext3);
                            if (json.has("KDYW")) {
                                Boolean kdyw = json.getBoolean("KDYW");
                                broadbandBusiness = kdyw ? "有" : "无";
                            }
                        } catch (Exception e) {
                            logger.debug("解析ext3字段失败，nodeId：{}", channelNode.getNodeId(), e);
                        }
                    }
                    nodeInfo.put("broadbandBusiness", broadbandBusiness);

                    // 是否授权网点 - 从ext4字段获取，1表示是，0表示否
                    String isAuthorizedNode = "否";
                    String ext4 = channelNode.getExt4();
                    if ("1".equals(ext4)) {
                        isAuthorizedNode = "是";
                    }
                    nodeInfo.put("isAuthorizedNode", isAuthorizedNode);

                    // 是否异业授权
                    String isOtherAuthorized = "否";
                    if (channelNode.getIsAuthorized() != null && channelNode.getIsAuthorized() == 1) {
                        isOtherAuthorized = "是";
                    }
                    nodeInfo.put("isOtherAuthorized", isOtherAuthorized);

                    // 归属网格
                    String gridName = channelNode.getGridName() != null ? channelNode.getGridName() : "";
                    // 如果gridName为空，尝试通过streetId查询
                    if (StringUtils.isBlank(gridName) && channelNode.getStreetId() != null) {
                        try {
                            gridName = channelNodeDao.getRealGridName(channelNode.getStreetId());
                        } catch (Exception e) {
                            logger.warn("查询网格名称失败，streetId：{}", channelNode.getStreetId(), e);
                        }
                    }
                    nodeInfo.put("gridName", gridName != null ? gridName : "");

                    // 查询对应的orgId
                    String orgId = "";
                    try {
                        ChannelOrgAgent channelOrgAgentQuery = new ChannelOrgAgent();
                        channelOrgAgentQuery.setAgentId(entity.getChannelEntityId());
                        List<ChannelOrgAgent> channelOrgAgentList = channelOrgAgentDao.query(channelOrgAgentQuery);
                        if (!channelOrgAgentList.isEmpty()) {
                            orgId = String.valueOf(channelOrgAgentList.get(0).getOrgId());
                        }
                    } catch (Exception e) {
                        logger.warn("查询orgId失败，channelEntityId：{}", entity.getChannelEntityId(), e);
                    }
                    nodeInfo.put("orgId", orgId);

                    resultList.add(nodeInfo);
                }
            }
        } catch (Exception e) {
            logger.error("根据orgId和orgName查询对应接口失败:", e);
            map.put("error", "根据orgId和orgName查询对应接口失败,接口异常！！！");
            resultList.add(map);
            return resultList;
        }

        logger.info("根据CRM传过来的orgId和orgName查询渠道网点对应信息 结束，返回{}条数据", resultList.size());
        return resultList;
    }

    public String convertStyleofManageScope(String manageScope) throws Exception {
        List<String> list = new ArrayList<String>();
        StringBuffer sb = new StringBuffer();
        if (manageScope != null) {
            JSONObject jsonObject = JSONObject.fromObject(manageScope);
            Map<String, Boolean> map = (Map) jsonObject;

            for (Map.Entry<String, Boolean> entry : map.entrySet()) {
                if (entry.getValue()) {
                    //把值为true的放到list中
                    list.add((String) entry.getKey());
                }
            }
            if (list.size() != 0) {
                if(map.size() < 30){
                    throw new Exception("存量渠道营业范围字段数据需要更新，");
                    // map 小于30 说明走存量数据，读 510544的配置数据转枚举
                    /*for (int key = 0; key < list.size(); key++) {
                        String codeName = list.get(key);
                        List<ChannelSysBaseType> channelSysBaseTypeList = channelSysBaseTypeDao.getChannelSysBaseType(510544, codeName, 1, null, null);
                        if (!CollectionUtils.isEmpty(channelSysBaseTypeList)) {
                            manageScope = channelSysBaseTypeList.get(0).getCodeNameNls();
                            sb.append(manageScope);
                            if (key < list.size() - 1) {
                                sb.append(",");
                            }

                        }
                    }*/
                }else{
                    for (int key = 0; key < list.size(); key++) {
                        String codeName = list.get(key);
                        List<ChannelSysBaseType> channelSysBaseTypeList = channelSysBaseTypeDao.getChannelSysBaseType(510545, codeName, 1, null, null);
                        if (!CollectionUtils.isEmpty(channelSysBaseTypeList)) {
                            manageScope = channelSysBaseTypeList.get(0).getCodeDesc();
                            sb.append(manageScope);
                            if (key < list.size() - 1) {
                                sb.append(",");
                            }

                        }
                    }
                }
            }

        }
        manageScope = sb.toString();
        return manageScope;
    }
}
